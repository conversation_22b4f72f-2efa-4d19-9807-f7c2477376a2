'use client';
import React, { useState } from 'react';
import { ShiftSettingForm } from '@/components/shift-settings-form';
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';

const SingleShiftPage = ({ params }) => {
	const { id } = params;
	const [mode, setMode] = useState('view');
	return (
		<Card>
			<CardHeader className="flex flex-row justify-between">
				<div>
					<CardTitle>View Details</CardTitle>
					<CardDescription>View shift details</CardDescription>
				</div>
				<div>
					<Button variant="outline" onClick={() => setMode('edit')}>
						Edit Shift
					</Button>
				</div>
			</CardHeader>
			<CardContent>
				<ShiftSettingForm mode={mode} shiftId={id} />
			</CardContent>
		</Card>
	);
};

export default SingleShiftPage;
