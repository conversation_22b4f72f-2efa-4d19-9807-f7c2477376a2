import { z } from 'zod';

const allowedImageMimeTypes = ['image/jpeg', 'image/png', 'image/jpg'];
const maxImageFileSize = 5 * 1024 * 1024; // 5MB

const allowedImageAndPdfMimeTypes = [
	'image/jpeg',
	'image/png',
	'image/jpg',
	'application/pdf',
];
const maxImageAndPdfFileSize = 5 * 1024 * 1024; // 5MB

export const imageMediaSchema = z
	.object({
		mimetype: z
			.string()
			.refine((type) => allowedImageMimeTypes.includes(type), {
				message: 'Invalid file type. Allowed: JPEG, PNG, MP4, PDF',
			}),
		size: z.number().max(maxImageFileSize, 'File size must be under 5MB'),
	})
	.strict();

export const imageAndPdfMediaSchema = z
	.object({
		mimetype: z
			.string()
			.refine((type) => allowedImageAndPdfMimeTypes.includes(type), {
				message: 'Invalid file type. Allowed: JPEG, PNG, MP4, PDF',
			}),
		size: z.number().max(maxImageAndPdfFileSize, 'File size must be under 5MB'),
	})
	.strict();
