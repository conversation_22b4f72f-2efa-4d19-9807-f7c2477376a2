'use client';

import { Separator } from '@/components/ui/separator';
import {
	Banknote,
	CalendarDays,
	ClipboardCheck,
	Fingerprint,
	HandCoins,
	Megaphone,
	MessageSquareText,
	Settings,
	Sparkles,
	TreePalm,
	User,
	Users,
} from 'lucide-react';
import Link from 'next/link';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { userRoles } from '@/lib/utils';
import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { CompanyTiles } from '@/components/company-tiles';
import { fetchCompanies } from '@/lib/features/glorified-client-admin/glorifiedClientAdminSlice';
import { Profile } from './profile/(profile-components)/profile';

export default function ClientAdminPage() {
	const dispatch = useAppDispatch();
	const { authenticatedUser } = useAppSelector((store) => store.auth);

	useEffect(() => {
		if (authenticatedUser?.role === userRoles.GLORIFIED_CLIENT_ADMIN) {
			dispatch(fetchCompanies());
		}
	}, [dispatch, authenticatedUser]);

	return (
		<>
			{authenticatedUser?.role !== userRoles.GLORIFIED_CLIENT_ADMIN ? (
				<Profile />
			) : (
				<div className="space-y-6 p-6">
					<div className="flex items-center justify-between">
						<div>
							<h3 className="text-lg font-medium">Dashboard</h3>
							<p className="text-sm text-muted-foreground">
								Welcome to your client admin dashboard
							</p>
						</div>
					</div>
					<Separator />
					<CompanyTiles />
				</div>
			)}
		</>
	);
}
