'use client';
import { motion } from 'motion/react';
import { HeroHighlight, Highlight } from './ui/hero-highlight';
import { DotBackground } from './ui/dot-background';
import Link from 'next/link';
// import { Button } from './ui/moving-border';
import logo from '@/assets/valluvalogo.png';
import Image from 'next/image';
import { Button } from './ui/button';
import { useCallback, useEffect } from 'react';
import axios from 'axios';

export function HeroHighlightSection() {
	const pingServer = useCallback(async () => {
		await axios.get('https://tms-backend-muzr.onrender.com/');
	}, []);

	useEffect(() => {
		pingServer();
	}, [pingServer]);

	return (
		<HeroHighlight>
			<div className="flex flex-col items-center justify-center gap-4">
				<motion.h1
					initial={{
						opacity: 0,
						y: 20,
					}}
					animate={{
						opacity: 1,
						y: [20, -5, 0],
					}}
					transition={{
						duration: 0.5,
						ease: [0.4, 0.0, 0.2, 1],
					}}
					className="text-2xl px-4 md:text-4xl lg:text-5xl font-bold text-neutral-700 dark:text-white max-w-4xl leading-relaxed lg:leading-snug text-center mx-auto"
				>
					Productivity starts here at{' '}
					<div className="flex items-center justify-center">
						<Image
							src={logo}
							alt="logo"
							width={144}
							className="object-contain w-24 md:w-36 lg:w-40"
						/>
					</div>
					{/* <div className="flex items-center justify-center gap-2">
						<Highlight className="text-black dark:text-white flex items-center justify-center gap-2">
						</Highlight>
					</div> */}
				</motion.h1>
				<Link href={'/login'}>
					{/* <Button
						borderRadius="1.75rem"
						className="bg-white dark:bg-slate-900 text-black dark:text-white border-neutral-200 dark:border-slate-800"
					>
						Get Started
					</Button> */}
					<Button variant="outline">Get Started</Button>
				</Link>
			</div>
		</HeroHighlight>
	);
}
