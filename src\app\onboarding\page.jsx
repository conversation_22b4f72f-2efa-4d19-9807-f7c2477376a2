'use client';
import logoPlaceholder from '@/assets/logo-placeholder-image.png';
import { BackgroundBoxes } from '@/components/background-boxes-component';
import { OnboardingBranchForm } from '@/components/onbaording-branch-form';
import { BusinessDetails } from '@/components/onboarding-business-details-form';
import { UploadLogo } from '@/components/onboarding-logo-upload-form';
import { TimeAndCurrencyDetails } from '@/components/onboarding-time-currency-form';
import { OnboardingDepartmentsForm } from '@/components/onboarding-departments-form';
import {
	Accordion,
	AccordionContent,
	AccordionItem,
	AccordionTrigger,
} from '@/components/ui/accordion';
import { AspectRatio } from '@/components/ui/aspect-ratio';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
	Card,
	CardContent,
	CardFooter,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';
import { DotBackground } from '@/components/ui/dot-background';
import { FileUpload } from '@/components/ui/file-upload';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { useAppSelector, useAppDispatch } from '@/lib/hooks';
import { AnimatePresence, motion } from 'framer-motion';
import Image from 'next/image';
import { useState, useEffect } from 'react';
import { slideVariants } from '@/lib/animation-variants';
import {
	collectData,
	setSteps,
} from '@/lib/features/client-admin/clientAdminSlice';
import { getCompanyDetails } from '@/lib/features/company-details/companyDetailsSlice';
import { Banknote, Calendar1, Clock10, CurrencyIcon } from 'lucide-react';
import { ScrollArea } from '@/components/ui/scroll-area';

export default function OnboardingPage() {
	const {
		companyData,
		steps,
		businessDetails,
		currencyAndTimeDetails,
		branches,
		departments,
		logo,
	} = useAppSelector((store) => store.clientAdmin);
	const { authenticatedUser } = useAppSelector((store) => store.auth);
	const { companyData: existingCompany } = useAppSelector(
		(store) => store.companyDetails
	);
	const dispatch = useAppDispatch();

	const [direction, setDirection] = useState(0);
	const [prevStep, setPrevStep] = useState(steps);

	useEffect(() => {
		if (authenticatedUser.isOnboardStepOneComplete) {
			dispatch(setSteps(2));
			dispatch(getCompanyDetails());
		}
	}, [authenticatedUser.isOnboardStepOneComplete, dispatch]);

	useEffect(() => {
		if (existingCompany) dispatch(collectData(existingCompany));
	}, [existingCompany, dispatch]);

	useEffect(() => {
		setDirection(steps > prevStep ? 1 : -1);
		setPrevStep(steps);
	}, [steps, prevStep]);

	const stepsConfig = [
		{ id: 0, title: 'Business Info', component: <UploadLogo /> },
		{ id: 1, title: 'Location Details', component: <BusinessDetails /> },
		{
			id: 2,
			title: 'Formats & Currency',
			component: <TimeAndCurrencyDetails />,
		},
		{ id: 3, title: 'Branches', component: <OnboardingBranchForm /> },
		{
			id: 4,
			title: 'Department Details',
			component: <OnboardingDepartmentsForm />,
		},
	];

	const progressPercentage = (steps / (stepsConfig.length - 1)) * 100;

	return (
		<ScrollArea className="w-full h-screen">
			<div className="flex items-center justify-center">
				<DotBackground disableRadiantBg={true}>
					<section className="grid grid-cols-12 gap-4 w-full p-6 md:p-10">
						<div className="col-span-12 md:col-span-4 min-h-96">
							<Card className="h-full w-full overflow-hidden">
								<CardHeader>
									<CardTitle className="flex items-center justify-between">
										<span>{stepsConfig[steps].title}</span>
										<Badge variant="outline">
											Step {steps + 1}/{stepsConfig.length}
										</Badge>
									</CardTitle>
									<Progress
										value={progressPercentage}
										className="h-2 transition-all duration-500 ease-in-out"
									/>
									<hr />
								</CardHeader>
								<CardContent key={steps}>
									<AnimatePresence mode="wait">
										<motion.div
											key={steps}
											custom={direction}
											variants={slideVariants}
											initial="enter"
											animate="center"
											exit="exit"
											className="w-full flex flex-col items-center justify-center "
										>
											{stepsConfig[steps]?.component}
										</motion.div>
									</AnimatePresence>
								</CardContent>
							</Card>
						</div>
						<div className="col-span-12 md:col-span-8 min-h-96">
							<Card className="h-full w-full">
								<CardHeader>
									<CardTitle>Details Entered</CardTitle>
									<hr />
								</CardHeader>
								<CardContent>
									<motion.div
										initial={{ opacity: 0 }}
										animate={{ opacity: 1 }}
										transition={{ duration: 0.5 }}
										className="space-y-6"
									>
										{/* Business Details Section */}
										<motion.div
											className="flex items-end justify-between mb-4"
											initial={{ y: 20, opacity: 0 }}
											animate={{ y: 0, opacity: 1 }}
											transition={{ delay: 0.1, duration: 0.5 }}
										>
											<article className="flex flex-col">
												<div className="flex items-end gap-2">
													<h2 className="font-medium text-2xl text-pretty transition-all">
														{businessDetails?.businessName}
													</h2>
													<p className="text-pretty mb-[0.125rem] transition-all">
														{businessDetails?.businessCountry?.name}
													</p>
												</div>
												<div className="flex items-center gap-2 transition-all">
													<p className="text-pretty text-sm">
														{businessDetails?.registration}
													</p>
												</div>
												<p>{businessDetails?.address}</p>
											</article>

											<motion.article
												className="w-24 h-24"
												initial={{ scale: 0.8, opacity: 0 }}
												animate={{ scale: 1, opacity: 1 }}
												transition={{ delay: 0.2, duration: 0.5 }}
											>
												<AspectRatio ratio={1 / 1} className="">
													<Image
														src={
															logo?.url ||
															businessDetails?.logo ||
															logoPlaceholder
														}
														alt={'Company Logo'}
														width={100}
														height={100}
														className="rounded-full object-fit object-center shadow"
													/>
												</AspectRatio>
											</motion.article>
										</motion.div>

										{/* Currency and Time Section */}
										{currencyAndTimeDetails && (
											<>
												<Separator />
												<motion.section
													className="grid grid-cols-3 gap-6 my-4"
													initial={{ y: 20, opacity: 0 }}
													animate={{ y: 0, opacity: 1 }}
													transition={{ delay: 0.3, duration: 0.5 }}
												>
													{currencyAndTimeDetails?.currency && (
														<Badge
															className="flex items-center justify-center gap-4 capitalize py-1"
															variant="outline"
														>
															<Banknote className="size-5" />
															{currencyAndTimeDetails.currency}
														</Badge>
													)}
													{currencyAndTimeDetails?.timeFormat && (
														<Badge
															className="flex items-center justify-center gap-4 capitalize py-1"
															variant="outline"
														>
															<Clock10 className="size-5" />
															{currencyAndTimeDetails.timeFormat}
														</Badge>
													)}
													{currencyAndTimeDetails?.dateFormat && (
														<Badge
															className="flex items-center justify-center gap-4 capitalize py-1"
															variant="outline"
														>
															<Calendar1 className="size-5" />
															{currencyAndTimeDetails.dateFormat}
														</Badge>
													)}
												</motion.section>
											</>
										)}

										{/* Branches Section */}
										{branches.length > 0 && (
											<>
												<Separator />
												<motion.div
													initial={{ y: 20, opacity: 0 }}
													animate={{ y: 0, opacity: 1 }}
													transition={{ delay: 0.4, duration: 0.5 }}
												>
													<Accordion
														type="single"
														collapsible
														className="w-full"
													>
														{branches.map((branch, index) => {
															// Access nested departments directly from the branch object
															const branchDepartments =
																branch.departments || [];

															return (
																<AccordionItem
																	value={branch.branchName}
																	key={branch.branchName}
																>
																	<AccordionTrigger className="capitalize text-lg">
																		{branch.branchName}
																	</AccordionTrigger>
																	<AccordionContent>
																		<div className="space-y-2">
																			<p className="font-medium">
																				Location: {branch.branchLocation}
																			</p>

																			{/* Departments List */}
																			{branchDepartments.length > 0 && (
																				<div className="mt-2 space-y-2">
																					<p className="font-medium">
																						Departments:
																					</p>
																					<ul className="pl-4 space-y-1">
																						{branchDepartments.map(
																							(dept, deptIndex) => (
																								<li
																									key={deptIndex}
																									className="flex justify-between"
																								>
																									<span>
																										{dept.departmentName}
																									</span>
																									{dept.departmentHead && (
																										<span className="text-sm text-muted-foreground">
																											Head:{' '}
																											{dept.departmentHead}
																										</span>
																									)}
																								</li>
																							)
																						)}
																					</ul>
																				</div>
																			)}
																		</div>
																	</AccordionContent>
																</AccordionItem>
															);
														})}
													</Accordion>
												</motion.div>
											</>
										)}
									</motion.div>
								</CardContent>
							</Card>
						</div>
					</section>
				</DotBackground>
			</div>
		</ScrollArea>
	);
}
