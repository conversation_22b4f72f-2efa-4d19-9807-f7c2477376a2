'use client';

import { useState } from 'react';
import {
	ChevronLeftIcon,
	ChevronRightIcon,
	Calendar,
	Clock,
	MapPin,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { format, addDays, subDays, isToday } from 'date-fns';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { cn } from '@/lib/utils';

export function CalendarCard () {
	const today = new Date();
	const [currentDate, setCurrentDate] = useState(today);
	const [isFlipping, setIsFlipping] = useState(false);
	const [nextDate, setNextDate] = useState(addDays(today, 1));

	const handleFlip = () => {
		if (isFlipping) return;
		setIsFlipping(true);
		const next = addDays(currentDate, 1);
		setNextDate(next);
		setTimeout(() => {
			setCurrentDate(next);
			setIsFlipping(false);
		}, 600);
	};

	const handlePrev = () => {
		if (isFlipping) return;
		setIsFlipping(true);
		const prev = subDays(currentDate, 1);
		setNextDate(prev);
		setTimeout(() => {
			setCurrentDate(prev);
			setIsFlipping(false);
		}, 600);
	};

	const handleNext = () => {
		if (isFlipping) return;
		setIsFlipping(true);
		const next = addDays(currentDate, 1);
		setNextDate(next);
		setTimeout(() => {
			setCurrentDate(next);
			setIsFlipping(false);
		}, 600);
	};

	// Sample events data
	const events = [
		{
			date: today,
			title: 'Team Standup',
			time: '9:00 AM',
			location: 'Conference Room A',
			description:
				'Daily team standup meeting to discuss progress and blockers.',
			type: 'work',
		},
		{
			date: today,
			title: 'Project Review',
			time: '2:00 PM',
			location: 'Meeting Room B',
			description: 'Quarterly project review with stakeholders.',
			type: 'meeting',
		},
		{
			date: today,
			title: 'Design Workshop',
			time: '11:00 AM',
			location: 'Creative Studio',
			description:
				'Collaborative design workshop for the new product features.',
			type: 'work',
		},
		{
			date: addDays(today, 1),
			title: 'Client Presentation',
			time: '10:00 AM',
			location: 'Main Hall',
			description: 'Present project deliverables to the client.',
			type: 'meeting',
		},
	];

	// Get events for the current date
	const getEventsForDate = (date) => {
		return events.filter(
			(event) => event.date.toDateString() === date.toDateString()
		);
	};

	const currentEvents = getEventsForDate(currentDate);

	const getEventTypeColor = (type) => {
		switch (type) {
			case 'work':
				return 'bg-purple-100 text-purple-700 border-purple-200';
			case 'meeting':
				return 'bg-blue-100 text-blue-700 border-blue-200';
			case 'social':
				return 'bg-green-100 text-green-700 border-green-200';
			case 'holiday':
				return 'bg-red-100 text-red-700 border-red-200';
			default:
				return 'bg-gray-100 text-gray-700 border-gray-200';
		}
	};

	return (
		<Card className="w-full">
			{/* Header with title and navigation */}
			<CardHeader>
				<div className="flex items-center justify-between">
					<CardTitle className="text-lg font-semibold text-card-foreground flex items-center gap-2">
						<Calendar className="h-5 w-5" />
						Calendar
					</CardTitle>
					<div className="flex items-center gap-2">
						<Button
							variant="outline"
							size="sm"
							onClick={handlePrev}
							className="h-8 w-8 p-0"
							disabled={isFlipping}
						>
							<ChevronLeftIcon className="h-4 w-4" />
						</Button>
						<Button
							variant="outline"
							size="sm"
							onClick={handleNext}
							className="h-8 w-8 p-0"
							disabled={isFlipping}
						>
							<ChevronRightIcon className="h-4 w-4" />
						</Button>
					</div>
				</div>
			</CardHeader>

			{/* Two Column Layout */}
			<CardContent>
				<div className="flex gap-4">
					{/* Left Column - Calendar */}
					<div className="grid bg-primary/70 p-3 rounded-lg items-center">
						{/* <div className="relative"> */}
							{/* Flip Calendar Container */}
							<div
								className="block relative w-[150px] h-[200px] perspective-[300px] bg-white rounded-xl shadow-lg cursor-pointer hover:scale-[1.02] transition-transform duration-200"
								onClick={handleFlip}
							>
								{/* Static Upper Card */}
								<div className="flex relative justify-center items-end w-full h-1/2 overflow-hidden border border-gray-200 border-b-[0.5px] rounded-t-xl bg-gradient-to-br from-white to-gray-50">
									<span className="text-[4rem] font-black text-teal-800/90 translate-y-1/2 select-none font-mono drop-shadow-md">
										{format(currentDate, 'dd')}
									</span>
								</div>

								{/* Static Lower Card */}
								<div className="flex relative justify-center items-start w-full h-1/2 overflow-hidden border border-gray-200 border-t-[0.5px] rounded-b-xl bg-gradient-to-br from-white to-gray-50">
									<span className="text-[4rem] font-black text-teal-800/90 -translate-y-1/2 select-none font-mono drop-shadow-md">
										{format(currentDate, 'dd')}
									</span>
								</div>

								{/* Animated Flip Cards */}
								{isFlipping && (
									<>
										{/* Fold Animation - Top Half */}
										<div
											className={cn(
												'flex justify-center items-end absolute left-0 top-0 w-full h-1/2 overflow-hidden backface-hidden',
												'bg-gradient-to-br from-white to-gray-50 border border-gray-200 border-b-[0.5px] rounded-t-xl',
												'shadow-lg origin-bottom animate-[fold_0.6s_cubic-bezier(0.455,0.03,0.515,0.955)_forwards]'
											)}
										>
											<span className="text-[4rem] font-black text-teal-800/90 translate-y-1/2 select-none font-mono drop-shadow-md">
												{format(currentDate, 'dd')}
											</span>
										</div>

										{/* Unfold Animation - Bottom Half */}
										<div
											className={cn(
												'flex justify-center items-start absolute left-0 top-1/2 w-full h-1/2 overflow-hidden backface-hidden',
												'bg-gradient-to-br from-white to-gray-50 border border-gray-200 border-t-[0.5px] rounded-b-xl',
												'shadow-lg origin-top animate-[unfold_0.6s_cubic-bezier(0.455,0.03,0.515,0.955)_forwards]'
											)}
										>
											<span className="text-[4rem] font-black text-teal-800/90 -translate-y-1/2 select-none font-mono drop-shadow-md">
												{format(nextDate, 'dd')}
											</span>
										</div>
									</>
								)}
							</div>
						{/* </div> */}
					</div>

					{/* Right Column - Events */}
					<Card className="flex-1">
						{/* Date Header - Fixed */}
						<CardHeader>
							<h4 className="font-semibold text-primary-foreground">
								{format(currentDate, 'EEEE, MMMM dd, yyyy')}
							</h4>
							{isToday(currentDate) && (
								<span className="inline-block mt-1 px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">
									Today
								</span>
							)}
						</CardHeader>

						{/* Events List - Scrollable */}
						<CardContent className='p-2'>
							<ScrollArea className="h-[100px] rounded-lg">
								<div className="space-y-3 pr-2">
									{currentEvents.length > 0 ? (
										currentEvents.map((event, index) => (
											<div
												key={index}
												className={`p-3 rounded-lg border ${getEventTypeColor(event.type)} scale-90`}
											>
												<div className="flex items-start justify-between mb-2">
													<h5 className="font-semibold text-sm">
														{event.title}
													</h5>
													<span className="text-xs px-2 py-1 bg-white rounded-full capitalize">
														{event.type}
													</span>
												</div>

												<div className="space-y-1 text-xs">
													<div className="flex items-center gap-2">
														<Clock className="h-3 w-3" />
														<span>{event.time}</span>
													</div>
													<div className="flex items-center gap-2">
														<MapPin className="h-3 w-3" />
														<span>{event.location}</span>
													</div>
													<p className="text-gray-600 mt-2 text-xs">
														{event.description}
													</p>
												</div>
											</div>
										))
									) : (
										<div className="text-center py-8 text-gray-500">
											<Calendar className="h-12 w-12 mx-auto mb-4 text-gray-300" />
											<p className="text-sm">No events scheduled</p>
											<p className="text-xs text-gray-400">
												Enjoy your free day!
											</p>
										</div>
									)}
								</div>
							</ScrollArea>
						</CardContent>
					</Card>
				</div>
			</CardContent>
		</Card>
	);
};

