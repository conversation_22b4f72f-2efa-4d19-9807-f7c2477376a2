import React, { useState, useEffect } from 'react';
import { CoffeeIcon, RefreshCwIcon, Laptop, Clock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';

import {
	Tooltip,
	TooltipContent,
	TooltipTrigger,
} from '@/components/ui/tooltip';
import { addMinutes } from 'date-fns';
import dayjs from 'dayjs';
import ConfirmDialog from '@/components/confirm-dialog';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import {
	getAttendanceLogs,
	getStatus,
	clockIn,
	clockOut,
	endBreak,
	startBreak,
} from '@/lib/features/attendance/attendanceSlice';
import { SimpleLoader } from '@/components/loading-component';
import { ScrollArea } from '@/components/ui/scroll-area';

const MarkAttendance = () => {
	const dispatch = useAppDispatch();
	const {
		attendanceLogs,
		currentLogStatus,
		isLoading,
		clockInDelay,
		clockOutDelay,
		clockInLimit,
		clockOutLimit,
	} = useAppSelector((state) => state.attendance);

	const [clockedIn, setClockedIn] = useState(
		currentLogStatus?.status === 'clockIn'
	);
	const [canClockIn, setCanClockIn] = useState(false);
	const [canClockOut, setCanClockOut] = useState(false);
	const [isOnBreak, setIsOnBreak] = useState(false);
	const [clockInTooltipMsg, setClockInTooltipMsg] = useState('');
	const [clockOutTooltipMsg, setClockOutTooltipMsg] = useState('');

	// Real-time clock states
	const [currentTime, setCurrentTime] = useState(new Date());
	const [totalWorkedTime, setTotalWorkedTime] = useState('00:00:00');
	const [clockInTime, setClockInTime] = useState(null);

	// Utility function to format time as HH:MM:SS using dayjs
	const formatTime = (date) => {
		return dayjs(date).format('HH:mm:ss');
	};

	// Utility function to format time as HH:MM for main display
	const formatMainTime = (totalSeconds) => {
		const hours = Math.floor(totalSeconds / 3600);
		const minutes = Math.floor((totalSeconds % 3600) / 60);
		return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
	};

	// Update clock in time when attendance logs change
	useEffect(() => {
		if (!attendanceLogs || attendanceLogs.length === 0) {
			setClockInTime(null);
			return;
		}

		// Find the most recent clock in without a corresponding clock out
		// const logs = [...attendanceLogs].reverse();
		const log = attendanceLogs[attendanceLogs.length - 1];
		let lastClockIn = null;

		// for (const log of logs) {
		// 	if (log.type === 'clockOut') {
		// 		break; // Found a clock out, so no active session
		// 	}
		// 	if (log.type === 'clockIn') {
		// 		lastClockIn = new Date(log.time);
		// 		break;
		// 	}
		// }
		if (log.type === 'clockIn' || log.type === 'end-break') {
			lastClockIn = new Date(log.time);
		}

		setClockInTime(lastClockIn);
	}, [attendanceLogs]);

	const checkClockingConditions = () => {
		if (!attendanceLogs || attendanceLogs.length === 0) {
			setCanClockIn(true);
			setClockInTooltipMsg('');
			return;
		}

		const now = new Date();
		const lastLog = attendanceLogs[attendanceLogs.length - 1];
		const lastLogTime = new Date(lastLog?.time);

		const totalClockIns = attendanceLogs.filter(
			(log) => log.type === 'clockIn'
		).length;
		const totalClockOuts = attendanceLogs.filter(
			(log) => log.type === 'clockOut'
		).length;

		if (lastLog?.type === 'start-break') {
			setClockedIn(true);
			setIsOnBreak(true); // you need to add this state to track break status
			setCanClockIn(false);
			setCanClockOut(false);
			setClockOutTooltipMsg('You are on a break');
		} else if (lastLog?.type === 'end-break') {
			setClockedIn(true);
			setCanClockIn(false);
			setCanClockOut(true);
			setIsOnBreak(false);
			setClockOutTooltipMsg('');
			// then apply your normal clock-in conditions here again or just reuse existing logic
		}

		if (lastLog?.type === 'clockIn') {
			setClockedIn(true);
			setCanClockIn(false);
			setClockInTooltipMsg('Already clocked in');

			if (totalClockOuts >= clockOutLimit) {
				setCanClockOut(false);
				setClockOutTooltipMsg(
					`You have reached the daily Clock Out limit (${clockOutLimit})`
				);
			} else if (addMinutes(lastLogTime, clockOutDelay) > now) {
				const waitMins = Math.ceil(
					(addMinutes(lastLogTime, clockOutDelay).getTime() - now.getTime()) /
						60000
				);
				setCanClockOut(false);
				setClockOutTooltipMsg(
					`Please wait ${waitMins} more minute(s) before Clocking Out`
				);
			} else {
				setCanClockOut(true);
				setClockOutTooltipMsg('');
			}
		} else if (lastLog?.type === 'clockOut') {
			setClockedIn(false);
			setCanClockOut(false);
			setClockOutTooltipMsg('Already clocked out');

			if (totalClockIns >= clockInLimit) {
				setCanClockIn(false);
				setClockInTooltipMsg(
					`You have reached the daily Clock In limit (${clockInLimit})`
				);
			} else if (addMinutes(lastLogTime, clockInDelay) > now) {
				const waitMins = Math.ceil(
					(addMinutes(lastLogTime, clockInDelay).getTime() - now.getTime()) /
						60000
				);
				setCanClockIn(false);
				setClockInTooltipMsg(
					`Please wait ${waitMins} more minute(s) before Clocking In`
				);
			} else {
				setCanClockIn(true);
				setClockInTooltipMsg('');
			}
		}
	};

	const getLogDetails = (type) => {
		switch (type) {
			case 'clockIn':
				return {
					title: 'Clock In',
					color: 'bg-green-500',
				};
			case 'clockOut':
				return {
					title: 'Clock Out',
					color: 'bg-red-500',
				};
			case 'start-break':
				return {
					title: 'Start Break',
					color: 'bg-yellow-500',
				};
			case 'end-break':
				return {
					title: 'End Break',
					color: 'bg-blue-500',
				};
			default:
				return {
					title: type,
					color: 'bg-gray-500',
				};
		}
	};

	const onRefresh = () => {
		dispatch(getAttendanceLogs());
		dispatch(getStatus());
	};

	useEffect(() => {
		checkClockingConditions();
	}, [
		attendanceLogs,
		clockInDelay,
		clockOutDelay,
		clockInLimit,
		clockOutLimit,
	]);
	useEffect(() => {
		if (!attendanceLogs) return;
		// Reverse iterate and grab the last break event
		const lastBreakEvent = [...attendanceLogs]
			.reverse()
			.find((log) => log.type === 'start-break' || log.type === 'end-break');
		setIsOnBreak(lastBreakEvent?.type === 'start-break');
	}, [attendanceLogs]);

	useEffect(() => {
		dispatch(getAttendanceLogs());
	}, [dispatch]);

	useEffect(() => {
		dispatch(getStatus());
	}, [dispatch]);

	// Real-time clock update effect
	useEffect(() => {
		const timer = setInterval(() => {
			setCurrentTime(new Date());

			// Calculate total worked time
			if (clockInTime) {
				const now = dayjs();
				const clockIn = dayjs(clockInTime);
				const totalSeconds = now.diff(clockIn, 'second');

				const hours = Math.floor(totalSeconds / 3600);
				const minutes = Math.floor((totalSeconds % 3600) / 60);
				const seconds = totalSeconds % 60;

				setTotalWorkedTime(
					`${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
				);
			} else {
				setTotalWorkedTime('00:00:00');
			}
		}, 1000);

		return () => clearInterval(timer);
	}, [clockInTime]);

	useEffect(() => {
		checkClockingConditions(); // initial check

		// Set up interval only if delay active (clockIn or clockOut)
		const now = new Date();
		const lastLog = attendanceLogs?.[attendanceLogs.length - 1];
		if (!lastLog) return; // no logs, no timer needed

		let delayEndTime;

		if (lastLog.type === 'clockIn') {
			delayEndTime = addMinutes(new Date(lastLog.time), clockOutDelay);
		} else if (lastLog.type === 'clockOut') {
			delayEndTime = addMinutes(new Date(lastLog.time), clockInDelay);
		} else {
			return;
		}

		if (delayEndTime <= now) {
			return; // delay already passed, no timer needed
		}

		const timer = setInterval(() => {
			checkClockingConditions();
		}, 1000 * 30); // every 30 seconds (or 60000 for every minute)

		return () => clearInterval(timer);
	}, [
		attendanceLogs,
		clockInDelay,
		clockOutDelay,
		clockInLimit,
		clockOutLimit,
	]);

	// if (isLoading) return <SimpleLoader />;

	const handleClockIn = async () => {
		await dispatch(clockIn());
		onRefresh();
	};

	const handleClockOut = async () => {
		await dispatch(clockOut());
		onRefresh();
	};

	const handleEndBreak = async () => {
		await dispatch(endBreak());
		onRefresh();
	};
	const handleStartBreak = async () => {
		await dispatch(startBreak());
		onRefresh();
	};

	return (
		<Card className="flex-1 min-w-[320px] max-w-full p-4 space-y-4">
			{/* Header with Title and Controls */}
			<div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
				<h2 className="text-lg font-semibold text-card-foreground flex items-center gap-2">
					Mark Attendance
				</h2>
				<div className="flex items-center gap-2">
					{isLoading && <SimpleLoader />}
					{!clockedIn ? (
						<Tooltip>
							<TooltipTrigger>
								<ConfirmDialog
									renderTrigger={
										<Button
											variant="outline"
											className="bg-green-100 text-green-800 hover:bg-green-200 border-green-300"
											disabled={isLoading || !canClockIn}
										>
											Clock In
										</Button>
									}
									title="Are you sure you want to clock in?"
									description={`You will not be able to Clock Out for the next ${clockInDelay} minutes`}
									confirmTextClassName="bg-green-100 text-green-800 hover:bg-green-200"
									confirmText="Clock In"
									cancelText="Cancel"
									onConfirm={handleClockIn}
								/>
							</TooltipTrigger>
							{!canClockIn && clockInTooltipMsg.length ? (
								<TooltipContent className="bg-red-100 text-red-800">
									<p>{clockInTooltipMsg}</p>
								</TooltipContent>
							) : null}
						</Tooltip>
					) : (
						<>
							{isOnBreak ? (
								<Button
									onClick={handleEndBreak}
									variant="outline"
									size="icon"
									disabled={isLoading}
									className="hover:bg-blue-50"
								>
									<Laptop className="h-4 w-4" />
								</Button>
							) : (
								<Button
									onClick={handleStartBreak}
									variant="outline"
									size="icon"
									disabled={isLoading}
									className="hover:bg-yellow-50"
								>
									<CoffeeIcon className="h-4 w-4" />
								</Button>
							)}
							<Tooltip>
								<TooltipTrigger>
									<ConfirmDialog
										renderTrigger={
											<Button
												className="bg-red-500 text-white hover:bg-red-600 shadow-md"
												disabled={isLoading || !canClockOut}
											>
												Clock Out
											</Button>
										}
										title="Are you sure you want to clock out?"
										description={`You will not be able to Clock In for the next ${clockOutDelay} minutes`}
										confirmTextClassName="bg-red-100 text-red-800 hover:bg-red-200"
										confirmText="Clock Out"
										cancelText="Cancel"
										onConfirm={handleClockOut}
									/>
								</TooltipTrigger>
								{!canClockOut && clockOutTooltipMsg.length ? (
									<TooltipContent className="bg-red-100 text-red-800">
										<p>{clockOutTooltipMsg}</p>
									</TooltipContent>
								) : null}
							</Tooltip>
							<Tooltip>
								<TooltipTrigger>
									<Button
										onClick={onRefresh}
										variant="outline"
										size="icon"
										disabled={isLoading}
										className="hover:bg-gray-50"
									>
										<RefreshCwIcon className="h-4 w-4" />
									</Button>
								</TooltipTrigger>
								<TooltipContent>
									<p>Refresh attendance data</p>
								</TooltipContent>
							</Tooltip>
						</>
					)}
				</div>
			</div>

			{/* Main Content Grid */}
			<div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
				{/* Left Section - Recent Activity (compact) */}
				<div className="lg:col-span-1">
					{/* <h3 className="text-sm font-medium text-gray-700 mb-3">Recent Activity</h3> */}

					<ScrollArea className="h-[167px]">
						{attendanceLogs && attendanceLogs.length > 0 ? (
							<div className="space-y-2">
								{attendanceLogs
									.slice(-3)
									.reverse()
									.map((log, index) => {
										const details = getLogDetails(log.type);
										const isLatest = index === 0;

										return (
											<div
												key={index}
												className={`flex items-center gap-3 p-2 rounded-md border ${
													isLatest
														? 'bg-blue-50 border-blue-200'
														: 'bg-gray-50 border-gray-200'
												} hover:bg-gray-100 transition-colors`}
											>
												{/* Simple colored dot */}
												<div
													className={`w-2 h-2 rounded-full ${details.color} flex-shrink-0`}
												></div>

												{/* Content */}
												<div className="flex-1 min-w-0">
													<div className="flex items-center justify-between">
														<span className="text-sm font-medium text-gray-900">
															{details.title}
														</span>
														{isLatest && (
															<span className="text-xs text-blue-600 font-medium">
																Latest
															</span>
														)}
													</div>
													<div className="text-xs text-gray-500">
														{format(new Date(log.time), 'MMM dd, HH:mm')}
													</div>
												</div>
											</div>
										);
									})}
							</div>
						) : (
							<div className="text-center py-6 text-gray-400">
								<p className="text-sm">No activity yet</p>
							</div>
						)}
					</ScrollArea>
				</div>

				{/* Right Section - Total Time Worked Display */}
				<div className="flex flex-col items-center justify-center space-y-2 min-w-[200px]">
					{/* Total Time Worked Title */}
					<h3 className="text-lg font-semibold text-foreground">
						Total Time Worked
					</h3>

					{/* Main Time Display - Large HH:MM */}
					<div className="text-6xl font-bold font-mono text-foreground tracking-wider">
						{clockInTime ? formatMainTime(dayjs().diff(dayjs(clockInTime), 'second')) : '00:00'}
					</div>

					{/* Current Time - Smaller below */}
					<div className="text-lg font-mono text-muted-foreground">
						{formatTime(currentTime)}
					</div>
				</div>
			</div>
		</Card>
	);
};

export default MarkAttendance;
