'use client';

import { MainSidebar } from '@/components/main-sidebar';
import ProtectedLayout from '@/components/protected-layout';
import { ScrollArea } from '@/components/ui/scroll-area';
import { userRoles } from '@/lib/utils';

export default function ClientAdminDashboardLayout({ children }) {
	return (
		<ProtectedLayout
			userType={[userRoles.GLORIFIED_CLIENT_ADMIN, userRoles.CLIENT_ADMIN]}
		>
			<MainSidebar
				role={[userRoles.CLIENT_ADMIN, userRoles.GLORIFIED_CLIENT_ADMIN]}
			>
				<ScrollArea className="h-full">{children}</ScrollArea>
			</MainSidebar>
		</ProtectedLayout>
	);
}
