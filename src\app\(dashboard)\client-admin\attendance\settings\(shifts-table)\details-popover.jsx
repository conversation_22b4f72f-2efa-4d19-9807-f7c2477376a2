'use client';
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from '@/components/ui/popover';
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
	Clock,
	Calendar,
	Shield,
	Wifi,
	MapPin,
	QrCode,
	Eye,
} from 'lucide-react';

export function DataTableCellContent({ type, value, details }) {
	if (type === 'shift') {
		return (
			<Popover>
				<PopoverTrigger asChild>
					<Button variant="link" className="p-0 h-auto font-medium text-left">
						{value}
					</Button>
				</PopoverTrigger>
				<PopoverContent className="w-80 p-0">
					<ShiftDetailsCard details={details} value={value} />
				</PopoverContent>
			</Popover>
		);
	}

	return <div>{value}</div>;
}

function ShiftDetailsCard({ details, value }) {
	const handleViewDetails = () => {
		window.location.href = `/shifts/${details.id}`;
	};

	const getFeatureBadges = () => {
		const features = [];
		if (details.features.wifiCheck)
			features.push({ name: 'WiFi Check', icon: Wifi });
		if (details.features.locationCheck)
			features.push({ name: 'Location Check', icon: MapPin });
		if (details.features.qrCheck)
			features.push({ name: 'QR Check', icon: QrCode });
		if (details.features.facialCheck)
			features.push({ name: 'Facial Check', icon: Eye });
		return features;
	};

	return (
		<Card className="border-0">
			<CardHeader className="pb-2">
				<div className="flex items-center justify-between">
					<div>
						<CardTitle className="text-lg">{value}</CardTitle>
						<CardDescription className="flex items-center gap-2">
							<Shield className="h-3 w-3" />
							Code: {details.code}
							{details.isDefault && (
								<Badge variant="default" className="ml-2">
									Default
								</Badge>
							)}
						</CardDescription>
					</div>
				</div>
			</CardHeader>
			<CardContent className="pb-2">
				<div className="space-y-3">
					<div className="flex items-center gap-2">
						<Clock className="h-4 w-4 text-muted-foreground" />
						<span className="text-sm">
							{details.startTime} - {details.endTime}
						</span>
					</div>

					<div className="flex items-center gap-2">
						<Calendar className="h-4 w-4 text-muted-foreground" />
						<span className="text-sm">
							{details.allowedDays?.length || 0} working days
						</span>
					</div>

					<Separator />

					<div className="space-y-2">
						<div className="text-sm font-medium">Features</div>
						<div className="flex flex-wrap gap-1">
							{getFeatureBadges().length > 0 ? (
								getFeatureBadges().map((feature) => (
									<Badge
										key={feature.name}
										variant="outline"
										className="text-xs"
									>
										<feature.icon className="h-3 w-3 mr-1" />
										{feature.name}
									</Badge>
								))
							) : (
								<span className="text-xs text-muted-foreground">
									Basic shift
								</span>
							)}
						</div>
					</div>

					<Separator />

					<Button
						variant="default"
						className="w-full"
						onClick={handleViewDetails}
					>
						View Full Details
					</Button>
				</div>
			</CardContent>
		</Card>
	);
}
