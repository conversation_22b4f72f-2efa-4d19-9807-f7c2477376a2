import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { customFetch, showErrors } from '@/lib/utils';
import { toast } from 'sonner';

const initialState = {
	designations: [],
	isLoading: false,
};

export const fetchDesignations = createAsyncThunk(
	'designation/fetchDesignations',
	async (params, thunkAPI) => {
		try {
			const { data } = await customFetch('/designations', {
				params: {
					...params,
				},
			});
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const updateDesignation = createAsyncThunk(
	'designation/updateDesignation',
	async (designationDetails, thunkAPI) => {
		try {
			const { data } = await customFetch.patch(
				'/designations',
				designationDetails
			);

			if (data?.success) {
				thunkAPI.dispatch(fetchDesignations());
			}

			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const addDesignation = createAsyncThunk(
	'designation/addDesignation',
	async (designationDetails, thunkAPI) => {
		try {
			const { data } = await customFetch.post(
				'/designations',
				designationDetails
			);

			if (data?.success) {
				thunkAPI.dispatch(fetchDesignations());
			}

			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const deleteDesignation = createAsyncThunk(
	'designation/deleteDesignation',
	async (designationIds, thunkAPI) => {
		try {
			const { data } = await customFetch.patch('/designations/remove', {
				designationIds,
			});

			if (data?.success) {
				thunkAPI.dispatch(fetchDesignations());
			}

			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

const designationSlice = createSlice({
	name: 'designation',
	initialState,
	reducers: {},
	extraReducers: (builder) => {
		builder
			.addCase(fetchDesignations.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(fetchDesignations.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.designations = payload.data.designations;
			})
			.addCase(fetchDesignations.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(updateDesignation.pending, (state) => {
				state.isLoading = true;
				toast.info('Updating Designation...');
			})
			.addCase(updateDesignation.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				toast.success(payload.message);
			})
			.addCase(updateDesignation.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(addDesignation.pending, (state) => {
				state.isLoading = true;
				toast.info('Creating Designation...');
			})
			.addCase(addDesignation.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				toast.success(payload.message);
			})
			.addCase(addDesignation.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(deleteDesignation.pending, (state) => {
				state.isLoading = true;
				toast.info('Deleting Designation...');
			})
			.addCase(deleteDesignation.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				toast.success(payload.message);
			})
			.addCase(deleteDesignation.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			});
	},
});

// export const {} = designationSlice.actions;
export default designationSlice.reducer;
