'use client';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import {
	ArrowUpDown,
	Building,
	Calendar,
	Globe,
	CreditCard,
	User,
} from 'lucide-react';
import { DataTableRowActions } from './row-actions';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { formatDate } from '@/lib/utils';

export const createColumns = (dispatch) => {
	return [
		{
			id: 'select',
			header: ({ table }) => (
				<Checkbox
					checked={table.getIsAllPageRowsSelected()}
					onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
					aria-label="Select all"
				/>
			),
			cell: ({ row }) => (
				<Checkbox
					checked={row.getIsSelected()}
					onCheckedChange={(value) => row.toggleSelected(!!value)}
					aria-label="Select row"
				/>
			),
			enableSorting: false,
			enableHiding: false,
		},
		{
			accessorKey: 'logo',
			header: 'Logo',
			cell: ({ row }) => (
				<Avatar className="h-10 w-10">
					<AvatarImage
						src={row.original.logo || '/placeholder.svg'}
						alt={row.original.businessName}
					/>
					<AvatarFallback>
						{row.original.businessName
							.split(' ')
							.map((n) => n[0])
							.join('')
							.toUpperCase()}
					</AvatarFallback>
				</Avatar>
			),
			enableSorting: false,
		},
		{
			accessorKey: 'businessName',
			header: ({ column }) => (
				<Button
					variant="ghost"
					onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
					className="p-0 hover:bg-transparent"
				>
					Company Name
					<ArrowUpDown className="ml-2 h-4 w-4" />
				</Button>
			),
			cell: ({ row }) => (
				<div className="flex items-center gap-2">
					<Building className="size-4 text-muted-foreground" />
					<span className="font-medium">{row.original.businessName}</span>
				</div>
			),
			enableSorting: true,
		},
		{
			accessorKey: 'registration',
			header: ({ column }) => (
				<Button
					variant="ghost"
					onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
					className="p-0 hover:bg-transparent"
				>
					Registration No.
					<ArrowUpDown className="ml-2 h-4 w-4" />
				</Button>
			),
			cell: ({ row }) => <div>{row.original.registration}</div>,
			enableSorting: true,
		},
		{
			accessorKey: 'businessCountry',
			header: 'Country',
			cell: ({ row }) => (
				<div className="flex items-center gap-2">
					<Globe className="size-4 text-muted-foreground" />
					<span>{row.original.businessCountry || 'N/A'}</span>
				</div>
			),
		},
		{
			accessorKey: 'currency',
			header: 'Currency',
			cell: ({ row }) => (
				<div className="flex items-center gap-2">
					<CreditCard className="size-4 text-muted-foreground" />
					<span>{row.original.currency || 'N/A'}</span>
				</div>
			),
		},
		{
			accessorKey: 'clientAdmin',
			header: 'Client Admin',
			cell: ({ row }) => {
				const isOwnerAdmin = row.original.owner === row.original.clientAdmin;
				return (
					<div className="flex items-center gap-2">
						<User className="size-4 text-muted-foreground" />
						<Badge variant={isOwnerAdmin ? 'outline' : 'default'}>
							{isOwnerAdmin ? 'Owner' : 'Appointed Admin'}
						</Badge>
					</div>
				);
			},
		},
		{
			accessorKey: 'createdAt',
			header: ({ column }) => (
				<Button
					variant="ghost"
					onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
					className="p-0 hover:bg-transparent"
				>
					Created At
					<ArrowUpDown className="ml-2 h-4 w-4" />
				</Button>
			),
			cell: ({ row }) => {
				const date = row.original.createdAt;
				return (
					<div className="flex items-center gap-2">
						<Calendar className="size-4 text-muted-foreground" />
						<span>{date ? formatDate(date) : 'N/A'}</span>
					</div>
				);
			},
			enableSorting: true,
		},
		{
			accessorKey: 'deleted',
			header: 'Status',
			cell: ({ row }) => (
				<div className="flex items-center justify-center">
					{row.original.deleted ? (
						<Badge variant="destructive">Inactive</Badge>
					) : (
						<Badge variant="success" className="bg-green-500">
							Active
						</Badge>
					)}
				</div>
			),
		},
		{
			id: 'actions',
			cell: ({ row }) => <DataTableRowActions row={row} dispatch={dispatch} />,
		},
	];
};
