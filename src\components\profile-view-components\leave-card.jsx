import React from 'react';
import { PlaneIcon, FileTextIcon, HourglassIcon, LockIcon } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '../ui/card';
import { ScrollArea } from '../ui/scroll-area';

export function LeaveCard() {
	return (
		<Card className="w-full h-full">
			<CardHeader>
				<div className="flex items-center justify-between">
				<h2 className="text-base font-semibold text-card-foreground flex items-center gap-2 h-8">
					Apply for Leave
				</h2>
				<div className="h-8 flex items-center gap-2">
					<Button
						variant="outline"
						className="h-8 bg-green-100 text-green-800 hover:bg-green-200"
					>
						Apply
					</Button>
				</div>
				</div>
			</CardHeader>
			<CardContent>
				<ScrollArea className="h-[200px] rounded-lg">
				Leave Details
			</ScrollArea>
			</CardContent>
			</Card>
	);
}
