import { useState } from 'react';
import { FileUpload } from './ui/file-upload';
import { Button } from './ui/button';
import { useAppDispatch } from '@/lib/hooks';
import {
	increaseSteps,
	setLogo,
	setSteps,
} from '@/lib/features/client-admin/clientAdminSlice';
const allowedImageMimeTypes = ['image/jpeg', 'image/png', 'image/jpg'];

export function UploadLogo() {
	const [files, setFiles] = useState([]);
	const dispatch = useAppDispatch();
	const handleFileUpload = async (files) => {
		setFiles(files);

		if (files.length === 0) {
			return;
		}
		if (!allowedImageMimeTypes.includes(files[0].type)) {
			toast.error(
				'Please upload a valid image, Allowed format : JPEG, PNG, JPG'
			);
			setFiles([]);
			return;
		}
		if (files[0].size > 1024 * 1024 * 5) {
			toast.error('File size must be under 5MB');
			setFiles([]);
			return;
		}
		const logo = {
			name: files[0].name,
			size: files[0].size,
			type: files[0].type,
			url: URL.createObjectURL(files[0]), // Generate URL for preview
		};
		dispatch(setLogo(logo));
	};
	return (
		<div className="w-full ">
			<FileUpload onChange={handleFileUpload} />
			{files.length > 0 && (
				<Button
					className="w-full"
					onClick={() => {
						dispatch(setSteps(1));
					}}
					disabled={files.length === 0}
				>
					Submit
				</Button>
			)}
		</div>
	);
}
