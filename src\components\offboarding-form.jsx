'use client';

import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent } from '@/components/ui/card';
import { useState, useRef } from 'react';
import { FileText, Upload, X } from 'lucide-react';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';

const offboardingFormSchema = z
	.object({
		dateOfLeave: z
			.string()
			.regex(/^\d{4}-\d{2}-\d{2}$/, 'Date must be in YYYY-MM-DD format')
			.transform((val) => new Date(val)),
		reason: z
			.string()
			.nonempty('Reason for leave is needed')
			.max(200, 'Reason should not be more than 200 characters long'),
		exitInterviewConducted: z.boolean().default(false),
		interviewConductedBy: z
			.string()
			.nonempty('Employee ID is required')
			.regex(/^[a-f\d]{24}$/i, 'Invalid Employee ID format'),
	})
	.superRefine((data, ctx) => {
		if (data.exitInterviewConducted && data.interviewConductedBy === '') {
			ctx.addIssue({
				path: ['interviewConductedBy'],
				message: 'Employee ID is required',
			});
		}
	});

export function EmployeeOffboardingForm() {
	const [uploadedFile, setUploadedFile] = useState(null);
	const [fileError, setFileError] = useState('');
	const fileInputRef = useRef(null);

	const form = useForm({
		resolver: zodResolver(offboardingFormSchema),
		defaultValues: {
			dateOfLeave: '',
			reason: '',
			exitInterviewConducted: false,
			interviewConductedBy: '',
		},
	});

	const watchExitInterviewConducted = form.watch('exitInterviewConducted');

	const handleFileChange = (e) => {
		const file = e.target.files[0];
		setFileError('');

		if (!file) {
			setUploadedFile(null);
			return;
		}

		// Check file type
		const validTypes = [
			'application/pdf',
			'image/jpeg',
			'image/png',
			'image/jpg',
		];
		if (!validTypes.includes(file.type)) {
			setFileError('Only PDF or image files are allowed');
			setUploadedFile(null);
			return;
		}

		// Check file size (max 5MB)
		if (file.size > 5 * 1024 * 1024) {
			setFileError('File size should not exceed 5MB');
			setUploadedFile(null);
			return;
		}

		setUploadedFile(file);
	};

	const removeFile = () => {
		setUploadedFile(null);
		if (fileInputRef.current) {
			fileInputRef.current.value = '';
		}
	};

	function onSubmit(data) {
		// Create FormData to handle file upload
		const formData = new FormData();

		// Add form data
		Object.entries(data).forEach(([key, value]) => {
			formData.append(key, value);
		});

		// Add file if exists
		if (uploadedFile) {
			formData.append('exitInterviewQuestionnaire', uploadedFile);
		}

		console.log('Form data submitted:', data);
		console.log('File uploaded:', uploadedFile);

		// Here you would typically send the formData to your server
		// For example: await fetch('/api/offboard-employee', { method: 'POST', body: formData });
	}

	// Mock data for employee selection
	const employees = [
		{ id: '645f3c5e7b1234567890abcd', name: 'John Smith' },
		{ id: '645f3c5e7b1234567890abce', name: 'Jane Doe' },
		{ id: '645f3c5e7b1234567890abcf', name: 'Robert Johnson' },
		{ id: '645f3c5e7b1234567890abd0', name: 'Emily Williams' },
		{ id: '645f3c5e7b1234567890abd1', name: 'Michael Brown' },
	];

	return (
		<Form {...form}>
			<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
				<div className="space-y-4">
					<h3 className="text-lg font-medium">Offboarding Details</h3>
					<Separator />

					<div className="grid grid-cols-1 md:grid-cols-3 gap-6">
						<FormField
							control={form.control}
							name="dateOfLeave"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Date of Leave</FormLabel>
									<FormControl>
										<Input
											type="date"
											{...field}
											min={new Date().toISOString().split('T')[0]}
										/>
									</FormControl>
									<FormDescription>
										The employee&apos;s last working day
									</FormDescription>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="exitInterviewConducted"
							render={({ field }) => (
								<FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
									<div className="space-y-0.5">
										<FormLabel className="text-base">
											Exit Interview Conducted
										</FormLabel>
										<FormDescription>
											Has an exit interview been conducted with this employee?
										</FormDescription>
									</div>
									<FormControl>
										<Switch
											checked={field.value}
											onCheckedChange={field.onChange}
										/>
									</FormControl>
								</FormItem>
							)}
						/>
						{watchExitInterviewConducted && (
							<FormField
								control={form.control}
								name="interviewConductedBy"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Interview Conducted By</FormLabel>
										<Select onValueChange={field.onChange} value={field.value}>
											<FormControl>
												<SelectTrigger>
													<SelectValue placeholder="Select employee" />
												</SelectTrigger>
											</FormControl>
											<SelectContent>
												{employees.map((employee) => (
													<SelectItem key={employee.id} value={employee.id}>
														{employee.name}
													</SelectItem>
												))}
											</SelectContent>
										</Select>
										<FormDescription>
											Employee who conducted the exit interview
										</FormDescription>
										<FormMessage />
									</FormItem>
								)}
							/>
						)}
					</div>

					<FormField
						control={form.control}
						name="reason"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Reason for Leaving</FormLabel>
								<FormControl>
									<Textarea
										placeholder="Enter reason for leaving"
										className="min-h-[120px] resize-none"
										{...field}
									/>
								</FormControl>
								<FormDescription>
									Provide details about why the employee is leaving
								</FormDescription>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>

				<div className="space-y-4">
					<h3 className="text-lg font-medium">Exit Interview Questionnaire</h3>
					<Separator />

					<Card>
						<CardContent className="pt-6">
							<div className="space-y-4">
								<FormItem>
									<FormLabel>Upload Exit Interview Questionnaire</FormLabel>
									<FormDescription>
										Upload the completed exit interview questionnaire (PDF or
										image file, max 5MB)
									</FormDescription>

									{!uploadedFile ? (
										<div className="mt-2">
											<div className="flex items-center justify-center w-full">
												<label
													htmlFor="file-upload"
													className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100"
												>
													<div className="flex flex-col items-center justify-center pt-5 pb-6">
														<Upload className="w-8 h-8 mb-2 text-gray-500" />
														<p className="mb-2 text-sm text-gray-500">
															<span className="font-semibold">
																Click to upload
															</span>{' '}
															or drag and drop
														</p>
														<p className="text-xs text-gray-500">
															PDF or Image (max 5MB)
														</p>
													</div>
													<input
														id="file-upload"
														ref={fileInputRef}
														type="file"
														accept=".pdf,.jpg,.jpeg,.png"
														className="hidden"
														onChange={handleFileChange}
													/>
												</label>
											</div>
											{fileError && (
												<p className="text-sm font-medium text-destructive mt-2">
													{fileError}
												</p>
											)}
										</div>
									) : (
										<div className="flex items-center p-2 mt-2 space-x-4 border rounded-md">
											<FileText className="h-10 w-10 text-blue-500" />
											<div className="flex-1 min-w-0">
												<p className="font-medium truncate">
													{uploadedFile.name}
												</p>
												<p className="text-sm text-gray-500">
													{(uploadedFile.size / 1024).toFixed(2)} KB
												</p>
											</div>
											<Button
												type="button"
												variant="ghost"
												size="sm"
												onClick={removeFile}
												className="text-red-500 hover:text-red-700"
											>
												<X className="h-4 w-4" />
											</Button>
										</div>
									)}
								</FormItem>
							</div>
						</CardContent>
					</Card>
				</div>

				<div className="flex justify-end space-x-4">
					<Button type="button" variant="outline">
						Cancel
					</Button>
					<Button type="submit">Submit Offboarding</Button>
				</div>
			</form>
		</Form>
	);
}
