'use client';
import { useEffect } from 'react';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { fetchProfilePageDetails } from '@/lib/features/employees/employeeSlice';
import { ProfileCard } from './profile-card';
import { MarkAttendance } from './mark-attendance';
import { CalendarCard } from './calendar-card';
import { ScrollArea } from '../ui/scroll-area';
import { LeaveCard } from './leave-card';
import { PlanCard } from './plan-card';
import { TaskCard } from './task-card';
import { NotificationCard } from './notification-card';



export function Profile() {
	const dispatch = useAppDispatch();
	const { authenticatedUser } = useAppSelector((store) => store.auth);
	const { userProfile } = useAppSelector((store) => store.employee);

	useEffect(() => {
		if (authenticatedUser?.userId) {
			dispatch(fetchProfilePageDetails(authenticatedUser.userId));
		}
	}, [dispatch, authenticatedUser?.userId]);

	return (
		<main className="w-full min-h-[calc(100vh-7rem)]">
			<section className="grid grid-cols-12 gap-4 h-full min-h-[calc(100vh-7rem)]">
				<article className="col-span-12 md:col-span-3 h-full">
					<ProfileCard userProfile={userProfile} />
				</article>
					<article className="col-span-12 md:col-span-9 h-full grid grid-rows-3 gap-4">
					<section className='grid grid-cols-2 gap-4'>
						<MarkAttendance />
						<CalendarCard/>
					</section>
					<section className='grid grid-cols-12 gap-4'>
						<div className='col-span-12 md:col-span-4'>
							<LeaveCard />
						</div>
						<div className='col-span-12 md:col-span-8'>
						<TaskCard/>
						</div>
					</section>
					<section className='grid grid-cols-12 gap-4'>
						<div className='col-span-12 md:col-span-8'>
						<NotificationCard />
						</div>
						<div className='col-span-12 md:col-span-4'>
						<PlanCard />
						</div>
						
					</section>
				</article>
			</section>
		</main>
	);
}