'use client';
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from '@/components/ui/popover';
import {
	Card,
	CardContent,
	CardDescription,
	CardFooter,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Mail, MessageSquare, FileText } from 'lucide-react';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';

export function DataTableCellContent({ type, value, details }) {
	if (type === 'employee') {
		return (
			<Popover>
				<PopoverTrigger asChild>
					<Button variant="link" className="p-0 h-auto font-medium text-left">
						{value}
					</Button>
				</PopoverTrigger>
				<PopoverContent className="w-80 p-0">
					<EmployeeDetailsCard details={details} value={value} />
				</PopoverContent>
			</Popover>
		);
	}

	return <div>{value}</div>;
}

function EmployeeDetailsCard({ details, value }) {
	const handleViewProfile = () => {
		window.location.href = `/client-admin/hr-module/employees-list/${details.id}`;
	};

	const getInitials = (name) => {
		return name
			.split(' ')
			.map((n) => n[0])
			.join('')
			.toUpperCase();
	};

	return (
		<Card className="border-0">
			<CardHeader className="pb-2">
				<div className="flex items-center gap-3">
					<Avatar className="h-12 w-12">
						<AvatarImage src={details.profilePhoto} alt={value} />
						<AvatarFallback>{getInitials(value)}</AvatarFallback>
					</Avatar>
					<div>
						<CardTitle className="text-lg">{value}</CardTitle>
						<CardDescription className="text-sm">
							{details.designation || 'Developer'}
						</CardDescription>
					</div>
				</div>
			</CardHeader>
			<CardContent className="pb-2">
				<div className="space-y-2">
					<div className="text-sm">
						<span className="font-medium">Department:</span>{' '}
						{details.department || 'IT Department'}
					</div>
					<div className="text-sm">
						<span className="font-medium">Email:</span> {details.email}
					</div>
					<div className="grid grid-cols-2 gap-2">
						<Button
							variant="outline"
							size="sm"
							className="w-full"
							onClick={() => (window.location.href = `mailto:${details.email}`)}
						>
							<Mail className="h-4 w-4 mr-2" />
							Email
						</Button>
						<Button
							variant="outline"
							size="sm"
							className="w-full"
							onClick={() => {
								/* Implement chat functionality */
							}}
						>
							<MessageSquare className="h-4 w-4 mr-2" />
							Chat
						</Button>
					</div>
				</div>
			</CardContent>
			<CardFooter>
				<Button
					variant="default"
					className="w-full"
					onClick={handleViewProfile}
				>
					<FileText className="h-4 w-4 mr-2" />
					View Full Profile
				</Button>
			</CardFooter>
		</Card>
	);
}

export function EmployeeDetailsPopover({ employee }) {
	console.log(` EmployeeDetailsPopover - employee:`, employee);
	// This component can be used elsewhere to show more detailed employee information
	// It's not directly used in the table but could be useful in other parts of app

	const personalDetails = employee.personalDetails;
	const name =
		personalDetails?.fullName || personalDetails?.nameOnNRIC || 'N/A';

	return (
		<div className="space-y-4">
			<div className="flex items-center gap-4">
				<Avatar className="h-16 w-16">
					<AvatarImage src={personalDetails?.profilePhoto} alt={name} />
					<AvatarFallback>
						{name
							.split(' ')
							.map((n) => n[0])
							.join('')
							.toUpperCase()}
					</AvatarFallback>
				</Avatar>
				<div>
					<h3 className="text-lg font-semibold">{name}</h3>
					<p className="text-sm text-muted-foreground">
						{personalDetails?.employeeOrgId || employee.employeeOrgId}
					</p>
					<Badge variant="outline" className="mt-1 capitalize">
						{personalDetails?.gender || 'N/A'}
					</Badge>
				</div>
			</div>

			<Separator />

			<div className="grid grid-cols-2 gap-4">
				<div>
					<p className="text-sm font-medium">Email</p>
					<p className="text-sm">{personalDetails?.email || 'N/A'}</p>
				</div>
				<div>
					<p className="text-sm font-medium">Phone</p>
					<p className="text-sm">{personalDetails?.mobile || 'N/A'}</p>
				</div>
				<div>
					<p className="text-sm font-medium">Joined</p>
					<p className="text-sm">
						{formatDate(personalDetails?.dateOfJoining)}
					</p>
				</div>
				<div>
					<p className="text-sm font-medium">Nationality</p>
					<p className="text-sm capitalize">
						{personalDetails?.nationality || 'N/A'}
					</p>
				</div>
			</div>

			{employee.familyDetails && (
				<>
					<Separator />
					<div>
						<p className="text-sm font-medium">Marital Status</p>
						<p className="text-sm capitalize">
							{employee.familyDetails.maritalStatus || 'N/A'}
						</p>
						{employee.familyDetails.spouseName && (
							<p className="text-sm mt-1">
								Spouse: {employee.familyDetails.spouseName}
							</p>
						)}
					</div>
				</>
			)}
		</div>
	);
}
