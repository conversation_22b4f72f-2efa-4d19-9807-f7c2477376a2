import { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
	Collapsible,
	CollapsibleContent,
	CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { Clock, ChevronUp, ChevronDown, X, Calendar } from 'lucide-react';
import { timeAgo, formatDate } from './utils/dateUtils';

// Helper function to capitalize strings
const capitalize = (str) => {
	if (!str) return '';
	return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
};

// Helper function to render diff-style data comparison
const renderDataDiff = (oldData, newData) => {
	if (!oldData && !newData) {
		return (
			<div className="text-sm text-gray-500 italic">No data available</div>
		);
	}

	// Handle arrays (like children, education, experience)
	if (Array.isArray(oldData) || Array.isArray(newData)) {
		const oldArray = Array.isArray(oldData) ? oldData : [];
		const newArray = Array.isArray(newData) ? newData : [];
		const maxLength = Math.max(oldArray.length, newArray.length);

		if (maxLength === 0) {
			return <div className="text-sm text-gray-500 italic">No items</div>;
		}

		return (
			<div className="space-y-4">
				{Array.from({ length: maxLength }, (_, index) => {
					const oldItem = oldArray[index];
					const newItem = newArray[index];

					if (!oldItem && newItem) {
						// New item added
						return (
							<div
								key={index}
								className="bg-green-50 border border-green-200 rounded-lg p-4"
							>
								<div className="flex items-center gap-2 mb-3">
									<div className="w-2 h-2 bg-green-500 rounded-full"></div>
									<span className="text-sm font-medium text-green-700">
										New Item {index + 1} (Added)
									</span>
								</div>
								{renderObjectDiff({}, newItem, true)}
							</div>
						);
					}

					if (oldItem && !newItem) {
						// Item removed
						return (
							<div
								key={index}
								className="bg-red-50 border border-red-200 rounded-lg p-4"
							>
								<div className="flex items-center gap-2 mb-3">
									<div className="w-2 h-2 bg-red-500 rounded-full"></div>
									<span className="text-sm font-medium text-red-700">
										Item {index + 1} (Removed)
									</span>
								</div>
								{renderObjectDiff(oldItem, {}, true)}
							</div>
						);
					}

					// Item exists in both, check for changes
					const hasChanges =
						JSON.stringify(oldItem) !== JSON.stringify(newItem);
					return (
						<div
							key={index}
							className={`rounded-lg p-4 border ${
								hasChanges
									? 'bg-amber-50 border-amber-200'
									: 'bg-gray-50 border-gray-200'
							}`}
						>
							<div className="flex items-center gap-2 mb-3">
								<div
									className={`w-2 h-2 rounded-full ${
										hasChanges ? 'bg-amber-500' : 'bg-gray-400'
									}`}
								></div>
								<span
									className={`text-sm font-medium ${
										hasChanges ? 'text-amber-700' : 'text-gray-600'
									}`}
								>
									Item {index + 1} {hasChanges ? '(Modified)' : '(Unchanged)'}
								</span>
							</div>
							{renderObjectDiff(oldItem || {}, newItem || {}, false)}
						</div>
					);
				})}
			</div>
		);
	}

	// Handle objects
	return renderObjectDiff(oldData || {}, newData || {}, false);
};

// Helper function to render object differences
const renderObjectDiff = (oldObj, newObj, isArrayItem = false) => {
	const allKeys = new Set([...Object.keys(oldObj), ...Object.keys(newObj)]);

	return (
		<div className="space-y-2">
			{Array.from(allKeys).map((key) => {
				const oldValue = oldObj[key];
				const newValue = newObj[key];
				let hasChanged = JSON.stringify(oldValue) !== JSON.stringify(newValue);
				if (Array.isArray(oldValue) && Array.isArray(newValue)) {
					hasChanged = oldValue.length !== newValue.length;
				}
				const isNew = !(key in oldObj) && key in newObj;
				const isRemoved = key in oldObj && !(key in newObj);

				return (
					<div
						key={key}
						className={`flex justify-between items-center py-2 px-3 rounded-md ${
							isNew
								? 'bg-green-100'
								: isRemoved
									? 'bg-red-100'
									: hasChanged
										? 'bg-yellow-100'
										: 'bg-transparent'
						}`}
					>
						<span className="text-sm font-medium text-gray-700 capitalize">
							{key.replace(/([A-Z])/g, ' $1').trim()}:
						</span>
						<div className="flex items-center gap-2 max-w-[60%]">
							{isRemoved ? (
								<span className="text-sm text-red-600 line-through">
									{formatFieldValue(oldValue)}
								</span>
							) : isNew ? (
								<span className="text-sm font-semibold text-green-600">
									{formatFieldValue(newValue)}
								</span>
							) : hasChanged ? (
								<div className="text-right">
									<div className="text-sm text-red-600 line-through">
										{formatFieldValue(oldValue)}
									</div>
									<div className="text-sm font-semibold text-green-600">
										{formatFieldValue(newValue)}
									</div>
								</div>
							) : (
								<span className="text-sm text-gray-600">
									{formatFieldValue(oldValue)}
								</span>
							)}
						</div>
					</div>
				);
			})}
		</div>
	);
};

// Helper function to format field values
const formatFieldValue = (value) => {
	if (value === null || value === undefined) return 'N/A';
	if (typeof value === 'boolean') return value ? 'Yes' : 'No';
	if (typeof value === 'string' && value.includes('T') && value.includes('Z')) {
		// Likely an ISO date string
		return formatDate(value);
	}
	if (Array.isArray(value)) return `${value.length} items`;
	if (typeof value === 'object') return 'Complex data';
	return String(value);
};

export function PendingRequests({ pendingChanges, onCancelRequest }) {
	const [showPendingRequests, setShowPendingRequests] = useState(false);

	if (pendingChanges.length === 0) {
		return null;
	}

	return (
		<Collapsible
			open={showPendingRequests}
			onOpenChange={setShowPendingRequests}
			className="mb-6 border rounded-lg overflow-hidden"
		>
			<div className="bg-amber-50 border-b border-amber-200">
				<CollapsibleTrigger className="flex items-center justify-between w-full p-4 text-left">
					<div className="flex items-center gap-2">
						<Clock className="h-5 w-5 text-amber-600" />
						<div>
							<h3 className="font-medium text-amber-800">
								Pending Change Requests
							</h3>
							<p className="text-sm text-amber-700">
								You have {pendingChanges.length} pending change request
								{pendingChanges.length > 1 ? 's' : ''}
							</p>
						</div>
					</div>
					<div className="flex items-center gap-2">
						<Badge className="bg-amber-100 text-amber-800 hover:bg-amber-200">
							{pendingChanges.length} Pending
						</Badge>
						{showPendingRequests ? (
							<ChevronUp className="h-5 w-5 text-amber-600" />
						) : (
							<ChevronDown className="h-5 w-5 text-amber-600" />
						)}
					</div>
				</CollapsibleTrigger>
			</div>
			<CollapsibleContent>
				<div className="p-6 bg-white">
					<div className="space-y-4">
						{pendingChanges.map((change) => (
							<Collapsible key={change._id}>
								<div className="border rounded-lg overflow-hidden bg-white shadow-sm hover:shadow-md transition-shadow">
									{/* Collapsible Header */}
									<CollapsibleTrigger className="w-full">
										<div className="bg-gradient-to-r from-amber-50 to-orange-50 px-6 py-4 border-b hover:from-amber-100 hover:to-orange-100 transition-colors">
											<div className="flex justify-between items-center">
												<div className="flex-1 text-left">
													<div className="flex items-center gap-3 mb-2">
														<Badge
															className={
																change.status === 'pending'
																	? 'bg-amber-100 text-amber-800 border-amber-200'
																	: change.status === 'approved'
																		? 'bg-green-100 text-green-800 border-green-200'
																		: 'bg-red-100 text-red-800 border-red-200'
															}
														>
															<Clock className="h-3 w-3 mr-1" />
															{change.status === 'pending'
																? 'Pending Approval'
																: change.status === 'approved'
																	? 'Approved'
																	: 'Rejected'}
														</Badge>
														<span className="text-sm text-muted-foreground">
															Submitted {timeAgo(change.submittedAt)}
														</span>
													</div>
													<h3 className="text-lg font-semibold text-gray-900">
														{capitalize(change.section.replace('-', ' '))}{' '}
														Information Update
													</h3>
													<p className="text-sm text-muted-foreground mt-1">
														Request ID: {change._id.slice(-8)} • Click to expand
														details
													</p>
												</div>
												<div className="flex items-center gap-2">
													{change.status === 'pending' && (
														<Button
															variant="ghost"
															size="sm"
															className="text-red-600 hover:text-red-700 hover:bg-red-50"
															onClick={(e) => {
																e.stopPropagation();
																onCancelRequest(change._id);
															}}
														>
															<X className="h-4 w-4 mr-1" />
															Cancel
														</Button>
													)}
													<ChevronDown className="h-5 w-5 text-gray-400 transition-transform group-data-[state=open]:rotate-180" />
												</div>
											</div>
										</div>
									</CollapsibleTrigger>

									{/* Collapsible Content */}
									<CollapsibleContent>
										<div className="p-6">
											<div className="space-y-4">
												<div className="flex items-center gap-3 mb-4">
													<div className="flex items-center gap-2">
														<div className="w-3 h-3 bg-gray-400 rounded-full"></div>
														<span className="text-sm text-gray-600">
															Unchanged
														</span>
													</div>
													<div className="flex items-center gap-2">
														<div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
														<span className="text-sm text-yellow-700">
															Modified
														</span>
													</div>
													<div className="flex items-center gap-2">
														<div className="w-3 h-3 bg-green-500 rounded-full"></div>
														<span className="text-sm text-green-600">
															Added
														</span>
													</div>
													<div className="flex items-center gap-2">
														<div className="w-3 h-3 bg-red-500 rounded-full"></div>
														<span className="text-sm text-red-600">
															Removed
														</span>
													</div>
												</div>

												<div className="bg-white rounded-lg border p-4">
													<h4 className="font-medium text-gray-800 mb-4 flex items-center gap-2">
														<span>Changes Overview</span>
														<span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
															{capitalize(change.section.replace('-', ' '))}
														</span>
													</h4>
													{renderDataDiff(change.oldData, change.newData)}
												</div>
											</div>

											{/* Reason */}
											{change.reason && (
												<div className="mt-6 pt-4 border-t">
													<div className="flex items-start gap-2">
														<div className="w-5 h-5 bg-amber-100 rounded-full flex items-center justify-center mt-0.5">
															<span className="text-amber-600 text-xs font-bold">
																!
															</span>
														</div>
														<div className="flex-1">
															<h5 className="font-medium text-gray-700 mb-1">
																Reason for Change
															</h5>
															<p className="text-sm text-gray-600 leading-relaxed">
																{change.reason}
															</p>
														</div>
													</div>
												</div>
											)}

											{/* Status Footer */}
											<div className="mt-6 pt-4 border-t bg-gray-50 -mx-6 -mb-6 px-6 py-4">
												<div className="flex items-center justify-between">
													<div className="flex items-center gap-2 text-sm text-muted-foreground">
														<Calendar className="h-4 w-4" />
														<span>
															Submitted on {formatDate(change.submittedAt)}
														</span>
													</div>
													{change.status === 'pending' && (
														<div className="flex items-center gap-2 text-sm text-amber-600">
															<Clock className="h-4 w-4" />
															<span>Awaiting manager approval</span>
														</div>
													)}
												</div>
											</div>
										</div>
									</CollapsibleContent>
								</div>
							</Collapsible>
						))}
					</div>
				</div>
			</CollapsibleContent>
		</Collapsible>
	);
}
