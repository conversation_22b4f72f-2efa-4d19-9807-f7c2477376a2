import { useEffect, useState } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle, Baby, Plus, X, Users } from 'lucide-react';
import { convertDateInputToIso } from './utils/dateUtils';

export function AddChildDialog({ open, onSubmit, onClose, currentFamilyData }) {
	// Helper function to convert YYYY-MM-DD format to ISO string for payload
	const convertDateInputToIso = (dateInput) => {
		if (!dateInput) return '';
		try {
			// Create date object from YYYY-MM-DD string
			const date = new Date(dateInput + 'T00:00:00.000Z');
			if (isNaN(date.getTime())) return '';

			// Return ISO string
			return date.toISOString();
		} catch (error) {
			console.error('Error converting date input to ISO:', error);
			return '';
		}
	};

	// Create family data validation schema
	const schema = z
		.object({
			maritalStatus: z.enum(['single', 'married', 'divorced', 'widowed'], {
				required_error: 'Please select marital status',
			}),
			spouseName: z.string().optional(),
			spouseEmploymentStatus: z
				.enum(['employed', 'unemployed', 'self-employed', 'retired'], {
					required_error: 'Please select spouse employment status',
				})
				.optional(),
			children: z
				.array(
					z.object({
						name: z.string().min(1, 'Child name is required'),
						dob: z.string().min(1, 'Date of birth is required'),
						gender: z.enum(['male', 'female'], {
							required_error: 'Please select a gender',
						}),
						nationality: z.string().min(1, 'Nationality is required'),
						birthCertificate: z.string().optional(),
					})
				)
				.optional(),
			reason: z
				.string()
				.min(1, 'Reason for family information update is required'),
		})
		.refine(
			(data) => {
				// If married, spouse name is required
				if (data.maritalStatus === 'married' && !data.spouseName?.trim()) {
					return false;
				}
				return true;
			},
			{
				message: 'Spouse name is required when married',
				path: ['spouseName'],
			}
		)
		.refine(
			(data) => {
				// If married, spouse employment status is required
				if (data.maritalStatus === 'married' && !data.spouseEmploymentStatus) {
					return false;
				}
				return true;
			},
			{
				message: 'Spouse employment status is required when married',
				path: ['spouseEmploymentStatus'],
			}
		);

	// Helper function to convert ISO date to YYYY-MM-DD format for form display
	const convertIsoToDateInput = (isoDate) => {
		if (!isoDate) return '';
		try {
			const date = new Date(isoDate);
			if (isNaN(date.getTime())) return '';
			return date.toISOString().split('T')[0];
		} catch (error) {
			console.error('Error converting ISO to date input:', error);
			return '';
		}
	};

	// Initialize form
	const form = useForm({
		resolver: zodResolver(schema),
		defaultValues: {
			maritalStatus: currentFamilyData?.maritalStatus || '',
			spouseName: currentFamilyData?.spouseName || '',
			spouseEmploymentStatus: currentFamilyData?.spouseEmploymentStatus || '',
			children: (currentFamilyData?.children || []).map((child) => ({
				...child,
				dob: convertIsoToDateInput(child.dob),
			})),
			reason: '',
		},
	});

	// Use field array for children management
	const { fields, append, remove } = useFieldArray({
		control: form.control,
		name: 'children',
	});

	// Reset form when dialog opens
	useEffect(() => {
		if (open && currentFamilyData) {
			form.reset({
				maritalStatus: currentFamilyData?.maritalStatus || '',
				spouseName: currentFamilyData?.spouseName || '',
				spouseEmploymentStatus: currentFamilyData?.spouseEmploymentStatus || '',
				children: (currentFamilyData?.children || []).map((child) => ({
					...child,
					dob: convertIsoToDateInput(child.dob),
				})),
				reason: '',
			});
		}
	}, [open, currentFamilyData, form]);

	// Watch marital status to show/hide spouse fields
	const maritalStatus = form.watch('maritalStatus');
	const showSpouseFields = maritalStatus && maritalStatus !== 'single';

	// Handle form submission
	const handleFormSubmit = async (data) => {
		const { reason, ...familyData } = data;

		// Convert children DOB from YYYY-MM-DD to ISO format for payload
		if (familyData.children && Array.isArray(familyData.children)) {
			familyData.children = familyData.children.map((child) => ({
				...child,
				dob: child.dob ? convertDateInputToIso(child.dob) : child.dob,
				// Calculate age
				age: child.dob
					? Math.floor(
							(new Date() - new Date(convertDateInputToIso(child.dob))) /
								(365.25 * 24 * 60 * 60 * 1000)
						)
					: 0,
			}));
		}

		// Clear spouse fields if single
		if (familyData.maritalStatus === 'single') {
			familyData.spouseName = '';
			familyData.spouseEmploymentStatus = '';
		}

		// Submit the request and wait for result
		const success = await onSubmit(
			'family',
			currentFamilyData,
			familyData,
			reason
		);

		// Only reset form if submission was successful
		if (success) {
			form.reset();
		}
	};

	// Add new child
	const addChild = () => {
		append({
			name: '',
			dob: '',
			gender: '',
			nationality: '',
			birthCertificate: '',
		});
	};

	if (!open) return null;

	return (
		<Dialog open={open} onOpenChange={onClose}>
			<DialogContent className="max-w-4xl w-[95vw] max-h-[90vh] overflow-hidden flex flex-col">
				<DialogHeader className="flex-shrink-0 pb-4 border-b">
					<DialogTitle className="text-xl font-semibold flex items-center gap-2">
						<Users className="h-5 w-5 text-blue-500" />
						Update Family Information
					</DialogTitle>
					<DialogDescription className="text-sm text-muted-foreground">
						Update your family information including marital status, spouse
						details, and children. All changes require approval from your
						manager.
					</DialogDescription>
				</DialogHeader>

				<Form {...form}>
					<form
						onSubmit={form.handleSubmit(handleFormSubmit)}
						className="flex-1 overflow-y-auto py-4"
					>
						<div className="space-y-6">
							<Alert className="border-amber-200 bg-amber-50">
								<AlertCircle className="h-4 w-4 text-amber-600" />
								<AlertTitle className="text-amber-800">
									Approval Required
								</AlertTitle>
								<AlertDescription className="text-amber-700">
									Family information changes will be reviewed by your reporting
									manager before being updated in your profile.
								</AlertDescription>
							</Alert>

							{/* Marital Status and Spouse Information */}
							<div className="space-y-4">
								<h3 className="text-lg font-semibold text-gray-900 border-b pb-2">
									Marital Status & Spouse Information
								</h3>

								<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
									<FormField
										control={form.control}
										name="maritalStatus"
										render={({ field }) => (
											<FormItem>
												<FormLabel>Marital Status *</FormLabel>
												<Select
													onValueChange={(value) => {
														field.onChange(value);
														// Clear spouse fields when changing to single
														if (value === 'single') {
															form.setValue('spouseName', '');
															form.setValue('spouseEmploymentStatus', '');
														}
													}}
													value={field.value}
												>
													<FormControl>
														<SelectTrigger>
															<SelectValue placeholder="Select marital status" />
														</SelectTrigger>
													</FormControl>
													<SelectContent>
														<SelectItem value="single">Single</SelectItem>
														<SelectItem value="married">Married</SelectItem>
														<SelectItem value="divorced">Divorced</SelectItem>
														<SelectItem value="widowed">Widowed</SelectItem>
													</SelectContent>
												</Select>
												<FormMessage />
											</FormItem>
										)}
									/>
								</div>

								{/* Spouse fields - only show when not single */}
								{showSpouseFields && (
									<div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
										<div className="md:col-span-2">
											<p className="text-sm text-blue-700 font-medium mb-3">
												Spouse Information (Required for married status)
											</p>
										</div>

										<FormField
											control={form.control}
											name="spouseName"
											render={({ field }) => (
												<FormItem>
													<FormLabel>Spouse Name *</FormLabel>
													<FormControl>
														<Input
															placeholder="Enter spouse's full name"
															{...field}
														/>
													</FormControl>
													<FormMessage />
												</FormItem>
											)}
										/>

										<FormField
											control={form.control}
											name="spouseEmploymentStatus"
											render={({ field }) => (
												<FormItem>
													<FormLabel>Spouse Employment Status *</FormLabel>
													<Select
														onValueChange={field.onChange}
														value={field.value}
													>
														<FormControl>
															<SelectTrigger>
																<SelectValue placeholder="Select employment status" />
															</SelectTrigger>
														</FormControl>
														<SelectContent>
															<SelectItem value="employed">Employed</SelectItem>
															<SelectItem value="unemployed">
																Unemployed
															</SelectItem>
															<SelectItem value="self-employed">
																Self-employed
															</SelectItem>
															<SelectItem value="retired">Retired</SelectItem>
														</SelectContent>
													</Select>
													<FormMessage />
												</FormItem>
											)}
										/>
									</div>
								)}
							</div>

							{/* Children Information */}
							<div className="space-y-4">
								<div className="flex items-center justify-between">
									<h3 className="text-lg font-semibold text-gray-900 border-b pb-2 flex-1">
										Children Information
									</h3>
									<Button
										type="button"
										variant="outline"
										size="sm"
										onClick={addChild}
										className="ml-4"
									>
										<Plus className="h-4 w-4 mr-2" />
										Add Child
									</Button>
								</div>

								{fields.length === 0 ? (
									<div className="text-center py-8 text-gray-500">
										<Baby className="h-12 w-12 mx-auto mb-3 text-gray-300" />
										<p>
											No children added yet. Click &quot;Add Child&quot; to add
											a child.
										</p>
									</div>
								) : (
									<div className="space-y-4">
										{fields.map((field, index) => (
											<div
												key={field.id}
												className="p-4 border rounded-lg bg-gray-50"
											>
												<div className="flex items-center justify-between mb-4">
													<h4 className="font-medium text-gray-900">
														Child {index + 1}
													</h4>
													<Button
														type="button"
														variant="ghost"
														size="sm"
														onClick={() => remove(index)}
														className="text-red-600 hover:text-red-700 hover:bg-red-50"
													>
														<X className="h-4 w-4" />
													</Button>
												</div>

												<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
													<FormField
														control={form.control}
														name={`children.${index}.name`}
														render={({ field }) => (
															<FormItem>
																<FormLabel>Child&apos;s Full Name *</FormLabel>
																<FormControl>
																	<Input
																		placeholder="Enter child's full name"
																		{...field}
																	/>
																</FormControl>
																<FormMessage />
															</FormItem>
														)}
													/>

													<FormField
														control={form.control}
														name={`children.${index}.dob`}
														render={({ field }) => (
															<FormItem>
																<FormLabel>Date of Birth *</FormLabel>
																<FormControl>
																	<Input type="date" {...field} />
																</FormControl>
																<FormMessage />
															</FormItem>
														)}
													/>

													<FormField
														control={form.control}
														name={`children.${index}.gender`}
														render={({ field }) => (
															<FormItem>
																<FormLabel>Gender *</FormLabel>
																<Select
																	onValueChange={field.onChange}
																	value={field.value}
																>
																	<FormControl>
																		<SelectTrigger>
																			<SelectValue placeholder="Select gender" />
																		</SelectTrigger>
																	</FormControl>
																	<SelectContent>
																		<SelectItem value="male">Male</SelectItem>
																		<SelectItem value="female">
																			Female
																		</SelectItem>
																	</SelectContent>
																</Select>
																<FormMessage />
															</FormItem>
														)}
													/>

													<FormField
														control={form.control}
														name={`children.${index}.nationality`}
														render={({ field }) => (
															<FormItem>
																<FormLabel>Nationality *</FormLabel>
																<FormControl>
																	<Input
																		placeholder="Enter nationality"
																		{...field}
																	/>
																</FormControl>
																<FormMessage />
															</FormItem>
														)}
													/>

													<FormField
														control={form.control}
														name={`children.${index}.birthCertificate`}
														render={({ field }) => (
															<FormItem className="md:col-span-2">
																<FormLabel>Birth Certificate Number</FormLabel>
																<FormControl>
																	<Input
																		placeholder="Enter birth certificate number (optional)"
																		{...field}
																	/>
																</FormControl>
																<FormMessage />
															</FormItem>
														)}
													/>
												</div>
											</div>
										))}
									</div>
								)}
							</div>

							{/* Reason for Change */}
							<FormField
								control={form.control}
								name="reason"
								render={({ field }) => (
									<FormItem>
										<FormLabel>
											Reason for Family Information Update *
										</FormLabel>
										<FormControl>
											<Textarea
												placeholder="Please provide a reason for updating your family information..."
												className="min-h-[100px]"
												{...field}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>

						<DialogFooter className="flex-shrink-0 pt-6 border-t mt-6">
							<Button type="button" variant="outline" onClick={onClose}>
								Cancel
							</Button>
							<Button type="submit">Submit Request</Button>
						</DialogFooter>
					</form>
				</Form>
			</DialogContent>
		</Dialog>
	);
}
