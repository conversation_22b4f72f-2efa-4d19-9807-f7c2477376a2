'use client';
import { But<PERSON> } from '@/components/ui/button';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import { MoreHorizontal, Edit, Trash } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';
import AddEditDialog from './add-edit-dialog';
import { deleteHoliday } from '@/lib/features/holiday/holidaySlice';

export function DataTableRowActions({ row, dispatch }) {
	const holiday = row.original;
	const [showDeleteDialog, setShowDeleteDialog] = useState(false);
	const [showStatusDialog, setShowStatusDialog] = useState(false);
	const [showAddEditDialog, setShowAddEditDialog] = useState(false);
	const [selectedHoliday, setSelectedHoliday] = useState(null);

	const handleView = () => {
		dispatch({
			type: 'holiday/viewHoliday',
			payload: holiday._id,
		});
		// Navigate to view page
		window.location.href = `/business-units/${holiday._id}`;
	};

	const handleEditClick = () => {
		setSelectedHoliday(holiday);
		setShowAddEditDialog(true);
	};

	const handleDelete = async () => {
		const result = await dispatch(deleteHoliday([holiday._id]));

		if (deleteHoliday.fulfilled.match(result)) {
			setShowDeleteDialog(false);
		}
	};

	const handleToggleStatus = () => {
		dispatch({
			type: 'holiday/toggleStatus',
			payload: {
				id: holiday._id,
				deleted: !holiday.deleted,
			},
		});

		toast({
			title: holiday.deleted ? 'Holiday activated' : 'Holiday deactivated',
			description: `${holiday.name} has been ${
				holiday.deleted ? 'activated' : 'deactivated'
			}.`,
		});
		setShowStatusDialog(false);
	};

	return (
		<>
			<DropdownMenu>
				<DropdownMenuTrigger asChild>
					<Button variant="ghost" className="h-8 w-8 p-0">
						<span className="sr-only">Open menu</span>
						<MoreHorizontal className="h-4 w-4" />
					</Button>
				</DropdownMenuTrigger>
				<DropdownMenuContent align="end">
					<DropdownMenuLabel>Actions</DropdownMenuLabel>
					{/* <DropdownMenuItem onClick={handleView}>
						<Eye className="mr-2 h-4 w-4" />
						View Details
					</DropdownMenuItem> */}
					<DropdownMenuItem onClick={handleEditClick}>
						<Edit className="mr-2 h-4 w-4" />
						Edit
					</DropdownMenuItem>
					<DropdownMenuSeparator />
					{/* <DropdownMenuItem onClick={() => setShowStatusDialog(true)}>
						{holiday.deleted ? (
							<>
								<CheckCircle className="mr-2 h-4 w-4 text-green-500" />
								Activate
							</>
						) : (
							<>
								<XCircle className="mr-2 h-4 w-4 text-destructive" />
								Deactivate
							</>
						)}
					</DropdownMenuItem> */}
					{/* <DropdownMenuSeparator /> */}
					<DropdownMenuItem
						onClick={() => setShowDeleteDialog(true)}
						className="text-destructive focus:text-destructive"
					>
						<Trash className="mr-2 h-4 w-4" />
						Delete
					</DropdownMenuItem>
				</DropdownMenuContent>
			</DropdownMenu>

			{/* Delete Confirmation Dialog */}
			<Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>
							Are you sure you want to delete this holiday?
						</DialogTitle>
						<DialogDescription>This action cannot be undone.</DialogDescription>
					</DialogHeader>
					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => setShowDeleteDialog(false)}
						>
							Cancel
						</Button>
						<Button variant="destructive" onClick={handleDelete}>
							Delete
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>

			{/* Status Change Confirmation Dialog */}
			<Dialog open={showStatusDialog} onOpenChange={setShowStatusDialog}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>
							{holiday.deleted
								? 'Are you sure you want to activate this holiday?'
								: 'Are you sure you want to deactivate this holiday?'}
						</DialogTitle>
						<DialogDescription>
							{holiday.deleted
								? 'This will make the holiday available again.'
								: 'This will make the holiday unavailable for new assignments.'}
						</DialogDescription>
					</DialogHeader>
					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => setShowStatusDialog(false)}
						>
							Cancel
						</Button>
						<Button
							variant={holiday.deleted ? 'default' : 'destructive'}
							onClick={handleToggleStatus}
						>
							{holiday.deleted ? 'Activate' : 'Deactivate'}
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>

			{/* Edit Holiday Dialog */}
			{showAddEditDialog && (
				<AddEditDialog
					title="Edit Holiday"
					desc="Edit holiday details"
					holiday={selectedHoliday}
					showAddEditDialog={showAddEditDialog}
					setShowAddEditDialog={setShowAddEditDialog}
				/>
			)}
		</>
	);
}
