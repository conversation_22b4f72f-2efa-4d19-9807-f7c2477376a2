import {
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { religions } from '@/data/religions';

export function PersonalFields({ form }) {
	return (
		<>
			<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
				<FormField
					control={form.control}
					name="nameOnNRIC"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Full Name (as per NRIC) *</FormLabel>
							<FormControl>
								<Input placeholder="Enter full name" {...field} />
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="employeeOrgId"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Employee ID *</FormLabel>
							<FormControl>
								<Input placeholder="Enter employee ID" {...field} />
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="dob"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Date of Birth *</FormLabel>
							<FormControl>
								<Input type="date" {...field} />
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="gender"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Gender *</FormLabel>
							<Select onValueChange={field.onChange} value={field.value}>
								<FormControl>
									<SelectTrigger>
										<SelectValue placeholder="Select gender" />
									</SelectTrigger>
								</FormControl>
								<SelectContent>
									<SelectItem value="male">Male</SelectItem>
									<SelectItem value="female">Female</SelectItem>
								</SelectContent>
							</Select>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="nationality"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Nationality *</FormLabel>
							<FormControl>
								<Input placeholder="Enter nationality" {...field} />
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="religion"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Religion</FormLabel>
							<Select onValueChange={field.onChange} value={field.value}>
								<FormControl>
									<SelectTrigger>
										<SelectValue placeholder="Select religion" />
									</SelectTrigger>
								</FormControl>
								<SelectContent>
									{religions.map((religion) => (
										<SelectItem key={religion.value} value={religion.value}>
											{religion.label}
										</SelectItem>
									))}
								</SelectContent>
							</Select>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="nric"
					render={({ field }) => (
						<FormItem>
							<FormLabel>NRIC/Passport Number *</FormLabel>
							<FormControl>
								<Input placeholder="Enter NRIC or passport number" {...field} />
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="countryDialCode"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Country Dial Code *</FormLabel>
							<FormControl>
								<Input placeholder="e.g., +65" {...field} />
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="mobile"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Mobile Number *</FormLabel>
							<FormControl>
								<Input placeholder="Enter mobile number" {...field} />
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="dateOfJoining"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Date of Joining *</FormLabel>
							<FormControl>
								<Input type="date" {...field} />
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
			</div>

			<FormField
				control={form.control}
				name="reason"
				render={({ field }) => (
					<FormItem>
						<FormLabel>Reason for Change *</FormLabel>
						<FormControl>
							<Textarea
								placeholder="Please provide a reason for these changes..."
								className="min-h-[100px]"
								{...field}
							/>
						</FormControl>
						<FormMessage />
					</FormItem>
				)}
			/>
		</>
	);
}
