import React, { useState } from 'react';
import { ChevronLeftIcon, ChevronRightIcon, Calendar, Clock, MapPin } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { format, addDays, subDays, isToday } from 'date-fns';

const CalendarCard = () => {
	const today = new Date();
	const [currentDate, setCurrentDate] = useState(today);
	const [isFlipping, setIsFlipping] = useState(false);
	const [nextDate, setNextDate] = useState(addDays(today, 1));

	const handleFlip = () => {
		if (isFlipping) return;

		setIsFlipping(true);

		// Calculate next date
		const next = addDays(currentDate, 1);
		setNextDate(next);

		// After animation completes, update current date
		setTimeout(() => {
			setCurrentDate(next);
			setIsFlipping(false);
		}, 600); // Match animation duration
	};

	const handlePrev = () => {
		if (isFlipping) return;

		setIsFlipping(true);

		// Calculate previous date
		const prev = subDays(currentDate, 1);
		setNextDate(prev);

		// After animation completes, update current date
		setTimeout(() => {
			setCurrentDate(prev);
			setIsFlipping(false);
		}, 600);
	};

	const handleNext = () => {
		if (isFlipping) return;

		setIsFlipping(true);

		// Calculate next date
		const next = addDays(currentDate, 1);
		setNextDate(next);

		// After animation completes, update current date
		setTimeout(() => {
			setCurrentDate(next);
			setIsFlipping(false);
		}, 600);
	};

	// Sample events data
	const events = [
		{
			date: today,
			title: 'Team Standup',
			time: '9:00 AM',
			location: 'Conference Room A',
			description: 'Daily team standup meeting to discuss progress and blockers.',
			type: 'work'
		},
		{
			date: today,
			title: 'Project Review',
			time: '2:00 PM',
			location: 'Meeting Room B',
			description: 'Quarterly project review with stakeholders.',
			type: 'meeting'
		},
		{
			date: today,
			title: 'Design Workshop',
			time: '11:00 AM',
			location: 'Creative Studio',
			description: 'Collaborative design workshop for the new product features.',
			type: 'work'
		},
		{
			date: today,
			title: 'Client Call',
			time: '4:00 PM',
			location: 'Video Conference',
			description: 'Weekly check-in call with the client to discuss project status.',
			type: 'meeting'
		},
		{
			date: today,
			title: 'Team Happy Hour',
			time: '6:00 PM',
			location: 'Local Pub',
			description: 'End of week celebration with the team.',
			type: 'social'
		},
		{
			date: today,
			title: 'Documentation Update',
			time: '1:00 PM',
			location: 'Office',
			description: 'Update project documentation and user guides.',
			type: 'work'
		},
		{
			date: addDays(today, 1),
			title: 'Client Presentation',
			time: '10:00 AM',
			location: 'Main Hall',
			description: 'Present project deliverables to the client.',
			type: 'meeting'
		},
		{
			date: addDays(today, 1),
			title: 'Team Lunch',
			time: '12:30 PM',
			location: 'Restaurant',
			description: 'Monthly team lunch and bonding session.',
			type: 'social'
		},
		{
			date: subDays(today, 1),
			title: 'Code Review',
			time: '3:00 PM',
			location: 'Dev Room',
			description: 'Weekly code review session.',
			type: 'work'
		}
	];

	// Get events for the current date
	const getEventsForDate = (date) => {
		return events.filter(event =>
			event.date.toDateString() === date.toDateString()
		);
	};

	const currentEvents = getEventsForDate(currentDate);

	const getEventTypeColor = (type) => {
		switch (type) {
			case 'work': return 'bg-purple-100 text-purple-700 border-purple-200';
			case 'meeting': return 'bg-blue-100 text-blue-700 border-blue-200';
			case 'social': return 'bg-green-100 text-green-700 border-green-200';
			case 'holiday': return 'bg-red-100 text-red-700 border-red-200';
			default: return 'bg-gray-100 text-gray-700 border-gray-200';
		}
	};

	// Optional: Auto-flip every 5 seconds for demo (disabled by default)
	// useEffect(() => {
	// 	const interval = setInterval(() => {
	// 		handleNext();
	// 	}, 5000);
	//
	// 	return () => clearInterval(interval);
	// }, [currentDate, isFlipping]);



	return (
		<div className="min-w-[280px] max-w-full p-4 space-y-4">
			{/* Header with title and navigation */}
			<div className="flex items-center justify-between">
				<h3 className="text-lg font-semibold text-card-foreground flex items-center gap-2">
					<Calendar className="h-5 w-5" />
					Calendar
				</h3>
				<div className="flex items-center gap-2">
					<Button
						variant="outline"
						size="sm"
						onClick={handlePrev}
						className="h-8 w-8 p-0"
						disabled={isFlipping}
					>
						<ChevronLeftIcon className="h-4 w-4" />
					</Button>
					<Button
						variant="outline"
						size="sm"
						onClick={handleNext}
						className="h-8 w-8 p-0"
						disabled={isFlipping}
					>
						<ChevronRightIcon className="h-4 w-4" />
					</Button>
				</div>
			</div>

			{/* Two Column Layout */}
			<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
				{/* Left Column - Calendar */}
				<div className="flex items-center justify-center min-h-[300px] bg-primary/70 p-6 rounded-lg">
					<div className="relative">
						{/* Flip Calendar Container */}
						<div
							className="flip-calendar-container cursor-pointer"
							onClick={handleFlip}
						>
							{/* Static Upper Card */}
							<div className="upper-card">
								<span className="date-digit">{format(currentDate, 'dd')}</span>
							</div>

							{/* Static Lower Card */}
							<div className="lower-card">
								<span className="date-digit">{format(currentDate, 'dd')}</span>
							</div>

							{/* Animated Flip Cards */}
							{isFlipping && (
								<>
									{/* Fold Animation - Top Half */}
									<div className="flip-card fold">
										<span className="date-digit">{format(currentDate, 'dd')}</span>
									</div>

									{/* Unfold Animation - Bottom Half */}
									<div className="flip-card unfold">
										<span className="date-digit">{format(nextDate, 'dd')}</span>
									</div>
								</>
							)}
						</div>
					</div>
				</div>

				{/* Right Column - Events */}
				<div className="h-[300px] bg-white rounded-lg border border-gray-200 p-4 flex flex-col">
					{/* Date Header - Fixed */}
					<div className="mb-4 pb-3 border-b border-gray-200 flex-shrink-0">
						<h4 className="text-lg font-semibold text-gray-900">
							{format(currentDate, 'EEEE, MMMM dd, yyyy')}
						</h4>
						{isToday(currentDate) && (
							<span className="inline-block mt-1 px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">
								Today
							</span>
						)}
					</div>

					{/* Events List - Scrollable */}
					<div className="flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
						<div className="space-y-3 pr-2">
							{currentEvents.length > 0 ? (
								currentEvents.map((event, index) => (
									<div
										key={index}
										className={`p-3 rounded-lg border ${getEventTypeColor(event.type)}`}
									>
										<div className="flex items-start justify-between mb-2">
											<h5 className="font-semibold text-sm">{event.title}</h5>
											<span className="text-xs px-2 py-1 bg-white rounded-full capitalize">
												{event.type}
											</span>
										</div>

										<div className="space-y-1 text-xs">
											<div className="flex items-center gap-2">
												<Clock className="h-3 w-3" />
												<span>{event.time}</span>
											</div>
											<div className="flex items-center gap-2">
												<MapPin className="h-3 w-3" />
												<span>{event.location}</span>
											</div>
											<p className="text-gray-600 mt-2 text-xs">
												{event.description}
											</p>
										</div>
									</div>
								))
							) : (
								<div className="text-center py-8 text-gray-500">
									<Calendar className="h-12 w-12 mx-auto mb-4 text-gray-300" />
									<p className="text-sm">No events scheduled</p>
									<p className="text-xs text-gray-400">Enjoy your free day!</p>
								</div>
							)}
						</div>
					</div>
				</div>
			</div>

			{/* Styles */}
			<style jsx>{`
				.flip-calendar-container {
					display: block;
					position: relative;
					width: 200px;
					height: 240px;
					perspective-origin: 50% 50%;
					perspective: 300px;
					background-color: white;
					border-radius: 12px;
					box-shadow: 0px 20px 20px -10px rgba(0, 0, 0, 0.2);
				}

				.upper-card, .lower-card {
					display: flex;
					position: relative;
					justify-content: center;
					width: 100%;
					height: 50%;
					overflow: hidden;
					border: 1px solid #e5e7eb;
					background: linear-gradient(145deg, #ffffff, #f8f9fa);
				}

				.upper-card {
					align-items: flex-end;
					border-bottom: 0.5px solid #e5e7eb;
					border-top-left-radius: 12px;
					border-top-right-radius: 12px;
				}

				.upper-card .date-digit {
					transform: translateY(50%);
				}

				.lower-card {
					align-items: flex-start;
					border-top: 0.5px solid #e5e7eb;
					border-bottom-left-radius: 12px;
					border-bottom-right-radius: 12px;
				}

				.lower-card .date-digit {
					transform: translateY(-50%);
				}

				.flip-card {
					display: flex;
					justify-content: center;
					position: absolute;
					left: 0;
					width: 100%;
					height: 50%;
					overflow: hidden;
					backface-visibility: hidden;
					background: linear-gradient(145deg, #ffffff, #f8f9fa);
					border: 1px solid #e5e7eb;
				}

				.flip-card.fold {
					top: 0%;
					align-items: flex-end;
					transform-origin: 50% 100%;
					transform: rotateX(0deg);
					border-top-left-radius: 12px;
					border-top-right-radius: 12px;
					border-bottom: 0.5px solid #e5e7eb;
					animation: fold 0.6s cubic-bezier(0.455, 0.03, 0.515, 0.955) forwards;
					transform-style: preserve-3d;
				}

				.flip-card.fold .date-digit {
					transform: translateY(50%);
				}

				.flip-card.unfold {
					top: 50%;
					align-items: flex-start;
					transform-origin: 50% 0%;
					transform: rotateX(180deg);
					border-bottom-left-radius: 12px;
					border-bottom-right-radius: 12px;
					border-top: 0.5px solid #e5e7eb;
					animation: unfold 0.6s cubic-bezier(0.455, 0.03, 0.515, 0.955) forwards;
					transform-style: preserve-3d;
				}

				.flip-card.unfold .date-digit {
					transform: translateY(-50%);
				}

				.date-digit {
					font-size: 4rem;
					font-weight: 900;
					color: hsl(171 44% 24%);
					text-shadow:
						0 2px 4px hsl(171, 44%, 24%, 0.2),
						0 4px 8px hsl(171, 44%, 24%, 0.1);
					user-select: none;
					font-family: 'Droid Sans Mono', monospace;
				}

				/* Hover effect */
				.flip-calendar-container:hover {
					transform: scale(1.02);
					transition: transform 0.2s ease;
				}

				/* Enhanced shadows during flip */
				.flip-card.fold,
				.flip-card.unfold {
					box-shadow:
						0 8px 25px rgba(0, 0, 0, 0.2),
						0 16px 40px rgba(0, 0, 0, 0.1);
				}

				@keyframes fold {
					0% {
						transform: rotateX(0deg);
					}
					100% {
						transform: rotateX(-180deg);
					}
				}

				@keyframes unfold {
					0% {
						transform: rotateX(180deg);
					}
					100% {
						transform: rotateX(0deg);
					}
				}
			`}</style>
		</div>
	);
};

export default CalendarCard;
