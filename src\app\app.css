@tailwind base;
@tailwind components;
@tailwind utilities;

html,
body {
	height: 100%;
	overflow: hidden;
}

body {
	font-family: Arial, Helvetica, sans-serif;
}

/* Hide default scrollbar */
::-webkit-scrollbar {
	display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
* {
	-ms-overflow-style: none; /* IE and Edge */
	scrollbar-width: none; /* Firefox */
}

@layer utilities {
	.text-balance {
		text-wrap: balance;
	}
}

@layer base {
	:root {
		--background: 0 0% 100%;
		--foreground: 356 60% 25%;
		--card: 0 0% 100%;
		--card-foreground: 356 60% 25%;
		--popover: 0 0% 100%;
		--popover-foreground: 356 60% 25%;
		--primary: 356 93% 65%;
		--primary-foreground: 0 0% 100%;
		--secondary: 220 70% 50%;
		--secondary-foreground: 0 0% 100%;
		--muted: 356 20% 95%;
		--muted-foreground: 356 40% 45%;
		--accent: 190 80% 50%;
		--accent-foreground: 0 0% 100%;
		--destructive: 0 84.2% 60.2%;
		--destructive-foreground: 0 0% 98%;
		--border: 356 20% 90%;
		--input: 356 20% 90%;
		--ring: 356 93% 65%;
		--radius: 0.5rem;
		--chart-1: 356 93% 65%;
		--chart-2: 220 70% 50%;
		--chart-3: 190 80% 50%;
		--chart-4: 280 70% 60%;
		--chart-5: 30 90% 60%;
		--chart-6: 160 70% 45%;
		--sidebar-background: 0 0% 98%;
		--sidebar-foreground: 356 60% 30%;
		--sidebar-primary: 356 93% 65%;
		--sidebar-primary-foreground: 0 0% 100%;
		--sidebar-accent: 190 80% 50%;
		--sidebar-accent-foreground: 0 0% 100%;
		--sidebar-border: 356 20% 90%;
		--sidebar-ring: 356 60% 50%;
	}

	.dark {
		--background: 356 30% 10%;
		--foreground: 0 0% 95%;
		--card: 356 30% 15%;
		--card-foreground: 0 0% 95%;
		--popover: 356 30% 8%;
		--popover-foreground: 0 0% 95%;
		--primary: 356 93% 65%;
		--primary-foreground: 0 0% 100%;
		--secondary: 220 70% 50%;
		--secondary-foreground: 0 0% 100%;
		--muted: 356 20% 20%;
		--muted-foreground: 0 0% 80%;
		--accent: 190 80% 50%;
		--accent-foreground: 0 0% 100%;
		--destructive: 0 62.8% 30.6%;
		--destructive-foreground: 0 85.7% 97.3%;
		--border: 356 20% 25%;
		--input: 356 20% 25%;
		--ring: 356 93% 65%;
		--chart-1: 356 93% 65%;
		--chart-2: 220 70% 60%;
		--chart-3: 190 80% 60%;
		--chart-4: 280 70% 70%;
		--chart-5: 30 90% 70%;
		--chart-6: 160 70% 55%;
		--sidebar-background: 356 30% 12%;
		--sidebar-foreground: 0 0% 95%;
		--sidebar-primary: 356 93% 65%;
		--sidebar-primary-foreground: 0 0% 100%;
		--sidebar-accent: 190 80% 60%;
		--sidebar-accent-foreground: 0 0% 100%;
		--sidebar-border: 356 20% 25%;
		--sidebar-ring: 356 70% 60%;
	}
}

@layer base {
	* {
		@apply border-border;
	}
	body {
		@apply bg-background text-foreground;
	}
}

/* different theme */

@tailwind base;
@tailwind components;
@tailwind utilities;

html,
body {
	height: 100%;
	overflow: hidden;
}

body {
	font-family: Arial, Helvetica, sans-serif;
}

/* Hide default scrollbar */
::-webkit-scrollbar {
	display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
* {
	-ms-overflow-style: none; /* IE and Edge */
	scrollbar-width: none; /* Firefox */
}

@layer utilities {
	.text-balance {
		text-wrap: balance;
	}
}

@layer base {
	:root {
		--background: 0 0% 100%;
		--foreground: 356 93% 25%;
		--card: 0 0% 100%;
		--card-foreground: 356 93% 25%;
		--popover: 0 0% 100%;
		--popover-foreground: 356 93% 25%;
		--primary: 356 93% 65%;
		--primary-foreground: 356 10% 98%;
		--secondary: 42 97% 51%;
		--secondary-foreground: 356 93% 25%;
		--muted: 356 30% 95%;
		--muted-foreground: 356 50% 40%;
		--accent: 42 97% 51%;
		--accent-foreground: 356 93% 25%;
		--destructive: 0 84.2% 60.2%;
		--destructive-foreground: 0 0% 98%;
		--border: 356 30% 90%;
		--input: 356 30% 90%;
		--ring: 356 93% 65%;
		--radius: 0.5rem;
		--chart-1: 356 93% 65%;
		--chart-2: 42 97% 51%;
		--chart-3: 356 60% 45%;
		--chart-4: 43 74% 66%;
		--chart-5: 27 87% 67%;
		--chart-6: 320 77% 55%;
		--sidebar-background: 0 0% 98%;
		--sidebar-foreground: 356 93% 30%;
		--sidebar-primary: 356 93% 65%;
		--sidebar-primary-foreground: 0 0% 100%;
		--sidebar-accent: 42 97% 51%;
		--sidebar-accent-foreground: 356 93% 25%;
		--sidebar-border: 356 30% 90%;
		--sidebar-ring: 356 50% 50%;
	}

	.dark {
		--background: 356 30% 10%;
		--foreground: 0 0% 95%;
		--card: 356 30% 15%;
		--card-foreground: 0 0% 95%;
		--popover: 356 30% 8%;
		--popover-foreground: 0 0% 95%;
		--primary: 356 93% 65%;
		--primary-foreground: 356 10% 98%;
		--secondary: 42 97% 51%;
		--secondary-foreground: 356 93% 25%;
		--muted: 356 30% 20%;
		--muted-foreground: 0 0% 80%;
		--accent: 42 97% 51%;
		--accent-foreground: 356 93% 25%;
		--destructive: 0 62.8% 30.6%;
		--destructive-foreground: 0 85.7% 97.3%;
		--border: 356 30% 25%;
		--input: 356 30% 25%;
		--ring: 356 93% 65%;
		--chart-1: 356 93% 65%;
		--chart-2: 42 97% 51%;
		--chart-3: 356 60% 55%;
		--chart-4: 43 74% 66%;
		--chart-5: 27 87% 67%;
		--chart-6: 320 77% 55%;
		--sidebar-background: 356 30% 12%;
		--sidebar-foreground: 0 0% 95%;
		--sidebar-primary: 356 93% 65%;
		--sidebar-primary-foreground: 0 0% 100%;
		--sidebar-accent: 42 97% 51%;
		--sidebar-accent-foreground: 356 93% 25%;
		--sidebar-border: 356 30% 25%;
		--sidebar-ring: 356 70% 60%;
	}
}

@layer base {
	* {
		@apply border-border;
	}
	body {
		@apply bg-background text-foreground;
	}
}

/* Previous Theme Teal */

@tailwind base;
@tailwind components;
@tailwind utilities;

html,
body {
	height: 100%;
	overflow: hidden;
}

body {
	font-family: Arial, Helvetica, sans-serif;
}

/* Hide default scrollbar */
::-webkit-scrollbar {
	display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
* {
	-ms-overflow-style: none; /* IE and Edge */
	scrollbar-width: none; /* Firefox */
}

@layer utilities {
	.text-balance {
		text-wrap: balance;
	}
}

@layer base {
	:root {
		--background: 0 0% 100%;
		--foreground: 171 44% 15%;
		--card: 0 0% 100%;
		--card-foreground: 171 44% 15%;
		--popover: 0 0% 100%;
		--popover-foreground: 171 44% 15%;
		--primary: 171 44% 24%;
		--primary-foreground: 171 44% 98%;
		--secondary: 42 97% 51%;
		--secondary-foreground: 171 44% 15%;
		--muted: 171 44% 95%;
		--muted-foreground: 171 44% 40%;
		--accent: 42 97% 51%;
		--accent-foreground: 171 44% 15%;
		--destructive: 0 84.2% 60.2%;
		--destructive-foreground: 0 0% 98%;
		--border: 171 44% 90%;
		--input: 171 44% 90%;
		--ring: 171 44% 24%;
		--radius: 0.5rem;
		--chart-1: 171 44% 24%;
		--chart-2: 42 97% 51%;
		--chart-3: 197 37% 24%;
		--chart-4: 43 74% 66%;
		--chart-5: 27 87% 67%;
		--chart-6: 347 77% 55%;
		--sidebar-background: 0 0% 98%;
		--sidebar-foreground: 171 44% 20%;
		--sidebar-primary: 171 44% 24%;
		--sidebar-primary-foreground: 0 0% 100%;
		--sidebar-accent: 42 97% 51%;
		--sidebar-accent-foreground: 171 44% 15%;
		--sidebar-border: 171 44% 90%;
		--sidebar-ring: 171 44% 40%;
	}

	.dark {
		--background: 171 44% 10%;
		--foreground: 0 0% 95%;
		--card: 171 44% 15%;
		--card-foreground: 0 0% 95%;
		--popover: 171 44% 8%;
		--popover-foreground: 0 0% 95%;
		--primary: 171 44% 50%;
		--primary-foreground: 171 44% 98%;
		--secondary: 42 97% 51%;
		--secondary-foreground: 171 44% 15%;
		--muted: 171 44% 20%;
		--muted-foreground: 0 0% 80%;
		--accent: 42 97% 51%;
		--accent-foreground: 171 44% 15%;
		--destructive: 0 62.8% 30.6%;
		--destructive-foreground: 0 85.7% 97.3%;
		--border: 171 44% 25%;
		--input: 171 44% 25%;
		--ring: 171 44% 50%;
		--chart-1: 171 44% 50%;
		--chart-2: 42 97% 51%;
		--chart-3: 197 37% 45%;
		--chart-4: 43 74% 66%;
		--chart-5: 27 87% 67%;
		--chart-6: 347 77% 55%;
		--sidebar-background: 171 44% 12%;
		--sidebar-foreground: 0 0% 95%;
		--sidebar-primary: 171 44% 50%;
		--sidebar-primary-foreground: 0 0% 100%;
		--sidebar-accent: 42 97% 51%;
		--sidebar-accent-foreground: 171 44% 15%;
		--sidebar-border: 171 44% 25%;
		--sidebar-ring: 171 44% 60%;
	}
}

@layer base {
	* {
		@apply border-border;
	}
	body {
		@apply bg-background text-foreground;
	}
}
