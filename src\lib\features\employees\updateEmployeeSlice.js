import { customFetch, showErrors } from '@/lib/utils';
import { toast } from 'sonner';
import { fetchEmployeeDetails } from './employeeSlice';

const { createSlice, createAsyncThunk } = require('@reduxjs/toolkit');

const initialState = {
	isLoading: false,
};

export const updateEmployeeDetailsPersonal = createAsyncThunk(
	'employeeUpdateDetails/updateEmployeeDetailsPersonal',
	async (employeeDetailsPersonalData, thunkAPI) => {
		try {
			const { data } = await customFetch.patch(
				`/employees/personal-details`,
				employeeDetailsPersonalData
			);

			if (data?.success) {
				thunkAPI.dispatch(
					fetchEmployeeDetails(employeeDetailsPersonalData.employeeId)
				);
			}

			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const updateEmployeeDetailsFamily = createAsyncThunk(
	'employeeUpdateDetails/updateEmployeeDetailsFamily',
	async (employeeDetailsFamilyData, thunkAPI) => {
		try {
			const { data } = await customFetch.patch(
				`/employees/family-details`,
				employeeDetailsFamilyData
			);

			if (data?.success) {
				thunkAPI.dispatch(
					fetchEmployeeDetails(employeeDetailsFamilyData.employeeId)
				);
			}

			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const updateEmployeeDetailsEducation = createAsyncThunk(
	'employeeUpdateDetails/updateEmployeeDetailsEducation',
	async (employeeDetailsEducationData, thunkAPI) => {
		try {
			const { data } = await customFetch.patch(
				`/employees/education-details`,
				employeeDetailsEducationData
			);

			if (data?.success) {
				thunkAPI.dispatch(
					fetchEmployeeDetails(employeeDetailsEducationData.employeeId)
				);
			}

			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const updateEmployeeDetailsExperience = createAsyncThunk(
	'employeeUpdateDetails/updateEmployeeDetailsExperience',
	async (employeeDetailsExperienceData, thunkAPI) => {
		try {
			const { data } = await customFetch.patch(
				`/employees/experience-details`,
				employeeDetailsExperienceData
			);

			if (data?.success) {
				thunkAPI.dispatch(
					fetchEmployeeDetails(employeeDetailsExperienceData.employeeId)
				);
			}

			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const updateEmployeeDetailsContact = createAsyncThunk(
	'employeeUpdateDetails/updateEmployeeDetailsContact',
	async (employeeDetailsContactData, thunkAPI) => {
		try {
			const { data } = await customFetch.patch(
				`/employees/contact-details`,
				employeeDetailsContactData
			);

			if (data?.success) {
				thunkAPI.dispatch(
					fetchEmployeeDetails(employeeDetailsContactData.employeeId)
				);
			}

			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const updateEmployeeDetailsEmployment = createAsyncThunk(
	'employeeUpdateDetails/updateEmployeeDetailsEmployment',
	async (employeeDetailsEmploymentData, thunkAPI) => {
		try {
			const { data } = await customFetch.patch(
				`/employees/employment-details`,
				employeeDetailsEmploymentData
			);

			if (data?.success) {
				thunkAPI.dispatch(
					fetchEmployeeDetails(employeeDetailsEmploymentData.employeeId)
				);
			}

			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

const employeeUpdateDetailsSlice = createSlice({
	name: 'employeeUpdateDetails',
	initialState,
	reducers: {},
	extraReducers: (builder) => {
		builder
			.addCase(updateEmployeeDetailsPersonal.pending, (state) => {
				state.isLoading = true;
				toast.info('Updating Personal Information...');
			})
			.addCase(
				updateEmployeeDetailsPersonal.fulfilled,
				(state, { payload }) => {
					state.isLoading = false;
					toast.success(payload.message);
				}
			)
			.addCase(updateEmployeeDetailsPersonal.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(updateEmployeeDetailsFamily.pending, (state) => {
				state.isLoading = true;
				toast.info('Updating Family Information...');
			})
			.addCase(updateEmployeeDetailsFamily.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				toast.success(payload.message);
			})
			.addCase(updateEmployeeDetailsFamily.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(updateEmployeeDetailsEducation.pending, (state) => {
				state.isLoading = true;
				toast.info('Updating Education Information...');
			})
			.addCase(
				updateEmployeeDetailsEducation.fulfilled,
				(state, { payload }) => {
					state.isLoading = false;
					toast.success(payload.message);
				}
			)
			.addCase(
				updateEmployeeDetailsEducation.rejected,
				(state, { payload }) => {
					state.isLoading = false;
					showErrors(payload);
				}
			)
			.addCase(updateEmployeeDetailsExperience.pending, (state) => {
				state.isLoading = true;
				toast.info('Updating Experience Information...');
			})
			.addCase(
				updateEmployeeDetailsExperience.fulfilled,
				(state, { payload }) => {
					state.isLoading = false;
					toast.success(payload.message);
				}
			)
			.addCase(
				updateEmployeeDetailsExperience.rejected,
				(state, { payload }) => {
					state.isLoading = false;
					showErrors(payload);
				}
			)
			.addCase(updateEmployeeDetailsContact.pending, (state) => {
				state.isLoading = true;
				toast.info('Updating Contact Information...');
			})
			.addCase(updateEmployeeDetailsContact.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				toast.success(payload.message);
			})
			.addCase(updateEmployeeDetailsContact.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(updateEmployeeDetailsEmployment.pending, (state) => {
				state.isLoading = true;
				toast.info('Updating Employment Information...');
			})
			.addCase(
				updateEmployeeDetailsEmployment.fulfilled,
				(state, { payload }) => {
					state.isLoading = false;
					toast.success(payload.message);
				}
			)
			.addCase(
				updateEmployeeDetailsEmployment.rejected,
				(state, { payload }) => {
					state.isLoading = false;
					showErrors(payload);
				}
			);
	},
});

export const {} = employeeUpdateDetailsSlice.actions;

export default employeeUpdateDetailsSlice.reducer;
