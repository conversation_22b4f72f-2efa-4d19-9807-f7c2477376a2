const { z } = require('zod');

export const loginSchema = z
	.object({
		email: z.string().email('Invalid Email Format').min(5),
		password: z.string(),
	})
	.strict();

export const registerEmailSchema = z
	.object({
		email: z.string().email('Invalid Email Format').min(5),
	})
	.strict();

export const forgotEmailSchema = z
	.object({
		email: z.string().email('Invalid Email Format').min(5),
	})
	.strict();

export const resetPasswordSchema = z
	.object({
		password: z
			.string()
			.regex(/^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[a-zA-Z]).{8,}$/gm, {
				message:
					'Password must contain at least 8 characters, one uppercase letter, one lowercase letter and one number',
			}),
		confirmPassword: z.string(),
	})
	.refine(
		({ password, confirmPassword }) => {
			return password === confirmPassword;
		},
		{
			message: 'Passwords do not match',
			path: ['confirmPassword'],
		}
	);

export const verifyEmailSchema = z
	.object({
		// email: z.string().email('Invalid Email Format').min(5),
		verificationToken: z
			.string()
			.min(6, 'Invalid Verification Token')
			.max(6, 'Invalid Verification Token')
			.regex(/^\d{6}$/, 'Invalid Verification Token'),
	})
	.strict();

export const registerSchema = z
	.object({
		// email: z.string().email('Invalid Email Format').min(5),
		password: z
			.string()
			.regex(/^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[a-zA-Z]).{8,}$/gm, {
				message:
					'Password must contain at least 8 characters, one uppercase letter, one lowercase letter and one number',
			}),
		confirmPassword: z.string(),
	})
	.refine(
		({ password, confirmPassword }) => {
			return password === confirmPassword;
		},
		{
			message: 'Passwords do not match',
			path: ['confirmPassword'],
		}
	);

export const updatePasswordSchema = z
	.object({
		password: z
			.string()
			.regex(/^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[a-zA-Z]).{8,}$/gm, {
				message:
					'Password must contain at least 8 characters, one uppercase letter, one lowercase letter and one number',
			}),
	})
	.strict();
