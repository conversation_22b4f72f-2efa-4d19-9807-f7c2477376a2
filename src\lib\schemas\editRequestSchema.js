import { z } from 'zod';
import { religions } from '@/data/religions';

// Helper function to get religion values
const religionValues = religions.map((r) => r.value);

// Base schema for common fields
const baseEditRequestSchema = z.object({
	reason: z
		.string()
		.min(10, 'Reason must be at least 10 characters')
		.max(500, 'Reason must not exceed 500 characters')
		.nonempty('Reason is required'),
});

// Personal details schema
export const personalDetailsEditSchema = baseEditRequestSchema.extend({
	nameOnNRIC: z
		.string()
		.min(3, 'Full Name must be at least 3 characters')
		.max(50, 'Full Name must not exceed 50 characters')
		.regex(/^[a-zA-Z\s]+$/, 'Full Name must contain only letters and spaces')
		.optional(),

	dob: z
		.string()
		.regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid date format')
		.optional(),

	gender: z
		.enum(['male', 'female', 'other'], {
			errorMap: () => ({ message: 'Please select a valid gender' }),
		})
		.optional(),

	icFinNumber: z
		.string()
		.min(8, 'IC/FIN number must be at least 8 characters')
		.max(12, 'IC/FIN number must not exceed 12 characters')
		.optional(),

	religion: z
		.enum(religionValues, {
			errorMap: () => ({ message: 'Please select a valid religion' }),
		})
		.optional(),

	race: z
		.enum(
			['chinese', 'eurasian', 'indian', 'malay', 'prefer-not-to-contribute'],
			{
				errorMap: () => ({ message: 'Please select a valid race' }),
			}
		)
		.optional(),
});

// Child schema for family details
const childSchema = z.object({
	name: z
		.string()
		.min(2, 'Child name must be at least 2 characters')
		.max(50, 'Child name must not exceed 50 characters')
		.regex(/^[a-zA-Z\s]+$/, 'Child name must contain only letters and spaces'),

	dob: z
		.string()
		.regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid date format')
		.refine((date) => {
			const birthDate = new Date(date);
			const today = new Date();
			const maxAge = new Date();
			maxAge.setFullYear(today.getFullYear() - 25); // Maximum 25 years old

			return birthDate >= maxAge && birthDate <= today;
		}, 'Child must be born within the last 25 years and not in the future'),

	age: z
		.number()
		.min(0, 'Age must be a positive number')
		.max(25, 'Age cannot exceed 25 years'),

	nationality: z.enum(
		[
			'singaporean',
			'malaysian',
			'indian',
			'chinese',
			'american',
			'british',
			'australian',
			'other',
		],
		{
			errorMap: () => ({ message: 'Please select a valid nationality' }),
		}
	),
});

// Family details schema
export const familyDetailsEditSchema = baseEditRequestSchema
	.extend({
		maritalStatus: z
			.enum(['single', 'married', 'divorced', 'widowed'], {
				errorMap: () => ({ message: 'Please select a valid marital status' }),
			})
			.optional(),

		spouseName: z.string().nullable().optional(),
		spouseEmploymentStatus: z
			.enum(
				['employed', 'unemployed', 'self-employed', 'retired', 'student', ''],
				{
					errorMap: () => ({
						message: 'Please select a valid employment status',
					}),
				}
			)
			.nullable()
			.optional(),

		children: z.array(childSchema).optional().default([]),
	})
	.superRefine((data, ctx) => {
		// If married, spouse name is required
		if (data.maritalStatus === 'married') {
			if (!data.spouseName) {
				ctx.addIssue({
					code: z.ZodIssueCode.custom,
					message: "Spouse Name is required when marital status is 'married'",
					path: ['spouseName'],
				});
			}
			if (!data.spouseEmploymentStatus) {
				ctx.addIssue({
					code: z.ZodIssueCode.custom,
					message:
						"Spouse Employment Status is required when marital status is 'married'",
					path: ['spouseEmploymentStatus'],
				});
			}
		}
	});

// Contact entry schema
const contactEntrySchema = z.object({
	name: z
		.string()
		.min(2, 'Contact name must be at least 2 characters')
		.max(50, 'Contact name must not exceed 50 characters')
		.regex(
			/^[a-zA-Z\s]+$/,
			'Contact name must contain only letters and spaces'
		),

	relationship: z.enum(
		['Spouse', 'Parent', 'Child', 'Sibling', 'Friend', 'Other'],
		{
			errorMap: () => ({ message: 'Please select a valid relationship' }),
		}
	),

	countryDialCode: z
		.string()
		.regex(/^\+\d{1,4}$/, 'Invalid country dial code format'),

	phone: z
		.string()
		.min(8, 'Phone number must be at least 8 digits')
		.max(15, 'Phone number must not exceed 15 digits')
		.regex(/^\d+$/, 'Phone number must contain only digits'),

	email: z.string().email('Invalid email format').optional(),

	type: z.enum(['emergency', 'reference'], {
		errorMap: () => ({ message: 'Please select a valid contact type' }),
	}),
});

// Contact details schema
export const contactDetailsEditSchema = baseEditRequestSchema.extend({
	contact: z
		.array(contactEntrySchema)
		.min(1, 'At least one contact entry is required')
		.default([]),
});

// Education entry schema
const educationEntrySchema = z
	.object({
		instituteName: z
			.string()
			.min(2, 'Institution name must be at least 2 characters')
			.max(100, 'Institution name must not exceed 100 characters'),

		qualification: z.enum(
			['HIGH_SCHOOL', 'DIPLOMA', 'BACHELOR', 'POST_GRADUATE', 'DOCTORATE'],
			{
				errorMap: () => ({ message: 'Please select a valid qualification' }),
			}
		),

		startDate: z
			.string()
			.regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid start date format'),

		endDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid end date format'),

		grade: z
			.string()
			.min(1, 'Grade must be at least 1 character')
			.max(10, 'Grade must not exceed 10 characters')
			.optional(),
	})
	.refine(
		(data) => {
			if (data.startDate && data.endDate) {
				return new Date(data.startDate) <= new Date(data.endDate);
			}
			return true;
		},
		{
			message: 'End date must be after start date',
			path: ['endDate'],
		}
	);

// Education details schema
export const educationDetailsEditSchema = baseEditRequestSchema.extend({
	education: z
		.array(educationEntrySchema)
		.min(1, 'At least one education entry is required')
		.default([]),
});

// Experience entry schema
const experienceEntrySchema = z
	.object({
		companyName: z
			.string()
			.min(2, 'Company name must be at least 2 characters')
			.max(100, 'Company name must not exceed 100 characters'),

		designation: z
			.string()
			.min(2, 'Job title must be at least 2 characters')
			.max(100, 'Job title must not exceed 100 characters'),

		periodFrom: z
			.string()
			.regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid start date format'),

		periodTo: z
			.string()
			.regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid end date format'),

		location: z
			.string()
			.min(2, 'Location must be at least 2 characters')
			.max(100, 'Location must not exceed 100 characters'),

		reasonForLeaving: z
			.string()
			.max(500, 'Reason for leaving must not exceed 500 characters')
			.optional(),
	})
	.refine(
		(data) => {
			if (data.periodFrom && data.periodTo) {
				return new Date(data.periodFrom) <= new Date(data.periodTo);
			}
			return true;
		},
		{
			message: 'End date must be after start date',
			path: ['periodTo'],
		}
	);

// Experience details schema
export const experienceDetailsEditSchema = baseEditRequestSchema.extend({
	experience: z
		.array(experienceEntrySchema)
		.min(1, 'At least one experience entry is required')
		.default([]),
});

// Function to get schema based on section
export const getEditRequestSchema = (section) => {
	switch (section) {
		case 'personal':
			return personalDetailsEditSchema;
		case 'family':
			return familyDetailsEditSchema;
		case 'contact':
			return contactDetailsEditSchema;
		case 'education':
			return educationDetailsEditSchema;
		case 'experience':
			return experienceDetailsEditSchema;
		default:
			return baseEditRequestSchema;
	}
};
