'use client';

import { useState } from 'react';
import { MessageSquareText, Search } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { cn } from '@/lib/utils';

export function ChatSidebar({ conversations = [], onSelectConversation }) {
	const [searchTerm, setSearchTerm] = useState('');

	const filteredConversations = conversations.filter((conversation) =>
		conversation.name.toLowerCase().includes(searchTerm.toLowerCase())
	);

	return (
		<div className="flex flex-col h-full">
			<div className="p-4 border-b">
				<div className="flex items-center justify-between mb-4">
					<h2 className="text-lg font-semibold">Chats</h2>
					<Button variant="ghost" size="icon">
						<MessageSquareText className="h-5 w-5" />
					</Button>
				</div>
				<div className="relative">
					<Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
					<Input
						type="search"
						placeholder="Search conversations..."
						className="pl-9"
						value={searchTerm}
						onChange={(e) => setSearchTerm(e.target.value)}
					/>
				</div>
			</div>
			<ScrollArea className="flex-1">
				<div className="p-2">
					{filteredConversations.map((conversation) => (
						<Button
							key={conversation.id}
							variant="ghost"
							className={cn(
								'w-full justify-start gap-2 mb-1',
								conversation.isActive && 'bg-accent'
							)}
							onClick={() => onSelectConversation(conversation)}
						>
							<div className="flex items-center gap-2">
								<div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
									{conversation.name.charAt(0)}
								</div>
								<div className="flex flex-col items-start">
									<span className="font-medium">{conversation.name}</span>
									<span className="text-xs text-muted-foreground truncate max-w-[200px]">
										{conversation.lastMessage}
									</span>
								</div>
							</div>
						</Button>
					))}
				</div>
			</ScrollArea>
		</div>
	);
}
