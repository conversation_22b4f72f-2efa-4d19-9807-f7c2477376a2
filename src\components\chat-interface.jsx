'use client';

import { useState } from 'react';
import { Send } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { cn } from '@/lib/utils';

export function ChatInterface({ conversation, onSendMessage }) {
	const [message, setMessage] = useState('');

	const handleSendMessage = () => {
		if (message.trim()) {
			onSendMessage(message);
			setMessage('');
		}
	};

	return (
		<div className="flex flex-col h-full">
			<div className="p-4 border-b">
				<h2 className="text-lg font-semibold">
					{conversation?.name || 'Select a conversation'}
				</h2>
			</div>
			<ScrollArea className="flex-1 p-4">
				<div className="space-y-4">
					{conversation?.messages?.map((msg, index) => (
						<div
							key={index}
							className={cn(
								'flex',
								msg.isOwn ? 'justify-end' : 'justify-start'
							)}
						>
							<div
								className={cn(
									'max-w-[70%] rounded-lg p-3',
									msg.isOwn ? 'bg-primary text-primary-foreground' : 'bg-muted'
								)}
							>
								<p className="text-sm">{msg.text}</p>
								<span className="text-xs opacity-70 mt-1 block">
									{new Date(msg.timestamp).toLocaleTimeString()}
								</span>
							</div>
						</div>
					))}
				</div>
			</ScrollArea>
			<div className="p-4 border-t">
				<div className="flex gap-2">
					<Input
						placeholder="Type a message..."
						value={message}
						onChange={(e) => setMessage(e.target.value)}
						onKeyDown={(e) => {
							if (e.key === 'Enter' && !e.shiftKey) {
								e.preventDefault();
								handleSendMessage();
							}
						}}
					/>
					<Button
						size="icon"
						onClick={handleSendMessage}
						disabled={!message.trim()}
					>
						<Send className="h-4 w-4" />
					</Button>
				</div>
			</div>
		</div>
	);
}
