import {
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Textarea } from '@/components/ui/textarea';
import { MultipleEntriesManager } from '../MultipleEntriesManager';

export function EducationFields({ form }) {
	const education = form.watch('education') || [];

	const handleEducationChange = (newEducation) => {
		form.setValue('education', newEducation);
	};

	return (
		<>
			<MultipleEntriesManager
				section="education"
				entries={education}
				onEntriesChange={handleEducationChange}
			/>

			<FormField
				control={form.control}
				name="reason"
				render={({ field }) => (
					<FormItem>
						<FormLabel>Reason for Change *</FormLabel>
						<FormControl>
							<Textarea
								placeholder="Please provide a reason for these changes..."
								className="min-h-[100px]"
								{...field}
							/>
						</FormControl>
						<FormMessage />
					</FormItem>
				)}
			/>
		</>
	);
}
