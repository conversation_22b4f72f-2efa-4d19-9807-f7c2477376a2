import { CreateCompany } from '@/components/create-company';
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import CompaniesTable from './(companies)/companies-table';

export default function AccountPage() {
	const tabTriggers = [
		{
			value: 'create-organization',
			name: 'Create Organization',
		},
		{
			value: 'view-organizations',
			name: 'View Organizations',
		},
	];

	const tabsContent = [
		{
			value: 'create-organization',
			title: 'Create Organization',
			desc: 'Create new organization.',
			component: <CreateCompany />,
			// dialogComponent: DesignationAddEditDialog,
			// dialogTitle: 'Add Designation',
			// dialogDesc: 'Add a new designation',
		},
		{
			value: 'view-organizations',
			title: 'Organizations',
			desc: 'View and manage organization admins.',
			component: <CompaniesTable />,
			// dialogComponent: DesignationAddEditDialog,
			// dialogTitle: 'Add Designation',
			// dialogDesc: 'Add a new designation',
		},
	];

	return (
		<div className="flex flex-col gap-4 w-full">
			<header className="flex flex-col gap-2">
				<h1 className="text-xl md:text-2xl font-semibold">Account</h1>
				<p className="font-medium text-gray-500">
					Manage organizations and their admins.
				</p>
				<Separator />
			</header>
			<main className="grid flex-1 items-start gap-4 md:gap-8 w-full mt-4">
				<Tabs defaultValue={tabTriggers[0].value}>
					<div className="flex items-center">
						<TabsList>
							{tabTriggers.map((trigger) => (
								<TabsTrigger
									key={trigger.value}
									value={trigger.value}
									className="flex-shrink-0 text-sm md:text-md whitespace-nowrap px-2 md:px-4"
								>
									{trigger.name}
								</TabsTrigger>
							))}
						</TabsList>
					</div>
					{tabsContent.map((content) => (
						<TabsContent key={content.value} value={content.value}>
							<Card>
								<CardHeader className="flex flex-row justify-between">
									<div>
										<CardTitle>{content.title}</CardTitle>
										<CardDescription>{content.desc}</CardDescription>
									</div>
									{/* {content.dialogComponent && (
										<>
											<Button
												className=""
												onClick={() => {
													setShowAddEditDialog(true);
													setIsAdd(true);
												}}
											>
												<PlusCircle className="h-5 w-5" />
												Add
											</Button>
											{showAddEditDialog && (
												<content.dialogComponent
													isAdd={isAdd}
													title={content.dialogTitle}
													desc={content.dialogDesc}
													showAddEditDialog={showAddEditDialog}
													setShowAddEditDialog={setShowAddEditDialog}
												/>
											)}
										</>
									)} */}
								</CardHeader>
								<CardContent>{content.component}</CardContent>
							</Card>
						</TabsContent>
					))}
				</Tabs>
			</main>
		</div>
	);
}
