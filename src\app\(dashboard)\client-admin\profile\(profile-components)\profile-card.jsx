'use client';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Mail, MapPin, PhoneCall, DollarSign } from 'lucide-react';
import Image from 'next/image';
import { useAppSelector } from '@/lib/hooks';
import fallbackImage from '@/assets/profile-image-fallback.png';
import coverImageFallback from '@/assets/cover-image-fallback.png';
import QR_Code from '@/assets/QR_Code.jpg';
import { AspectRatio } from '@/components/ui/aspect-ratio';
import { Button } from '@/components/ui/button';
import {
	Dialog,
	DialogClose,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from '@/components/ui/dialog';
import { useState } from 'react';

// Helper functions to replace the imported ones
const formatDate = (dateString) => {
	if (!dateString) return '';
	const date = new Date(dateString);
	return date.toLocaleDateString('en-US', {
		year: 'numeric',
		month: 'long',
		day: 'numeric',
	});
};

const getInitials = (name) => {
	if (!name) return '';
	return name
		.split(' ')
		.map((part) => part[0])
		.join('')
		.toUpperCase();
};

const PayslipDialog = ({ QRImage = QR_Code, open, setOpen }) => {
	return (
		<Dialog open={open} onOpenChange={setOpen}>
			<DialogContent className="sm:max-w-md">
				<DialogHeader>
					<DialogTitle>Payslip Verification</DialogTitle>
					<DialogDescription>
						Scan the QR code using the mobile app to authenticate your payslip.
					</DialogDescription>
				</DialogHeader>
				<div className="rounded-t-xl overflow-hidden flex items-center justify-center h-[300px]">
					<Image
						src={QRImage}
						alt="QR Code"
						className="object-contain"
						width={300}
						height={300}
					/>
				</div>

				<DialogFooter className="sm:justify-end">
					<DialogClose asChild>
						<Button
							type="button"
							variant="destructive"
							onClick={() => setOpen(false)}
						>
							Cancel
						</Button>
					</DialogClose>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
};

const ProfileCard = ({ userProfile }) => {
	const [payslipDialogOpen, setPayslipDialogOpen] = useState(false);

	if (!userProfile) {
		return null;
	}

	const { personalDetails, designation, email, department, reportingTo } =
		userProfile;

	return (
		<Card className="w-full sm:w-fit min-w-[300px] overflow-hidden h-fit sm:h-full">
			<div className="h-[120px] bg-background rounded-t-xl overflow-hidden justify-start">
				<AspectRatio ratio={1 / 1} className="w-full h-full">
					<Image
						src={personalDetails?.coverImage || coverImageFallback}
						alt={personalDetails?.nameOnNRIC || 'Employee Name'}
						className="w-full h-full object-fit"
						fill
					/>
				</AspectRatio>
			</div>

			<CardHeader className="flex justify-center -mt-14 px-5 pt-0 pb-3">
				<Avatar className="w-24 h-24 rounded-full bg-cover bg-center shadow-lg border-4 border-white">
					<AvatarImage
						src={personalDetails?.profilePhoto || '/placeholder.svg'}
						alt={personalDetails?.nameOnNRIC || 'Profile'}
					/>
					<AvatarFallback className="text-xl font-semibold uppercase">
						{personalDetails?.profilePhoto ? (
							getInitials(personalDetails?.nameOnNRIC)
						) : (
							<Image
								alt={personalDetails?.nameOnNRIC || 'Profile'}
								src={fallbackImage || '/placeholder.svg'}
								className="w-24 h-24 rounded-full object-contain"
								width={100}
								height={100}
							/>
						)}
					</AvatarFallback>
				</Avatar>

				<div className="flex flex-col">
					<h2 className="text-2xl font-semibold text-card-foreground">
						{personalDetails?.nameOnNRIC || 'Employee Name'}
					</h2>
					{designation && (
						<h4 className="text-sm font-medium text-card-foreground opacity-80">
							{designation}
						</h4>
					)}
				</div>
			</CardHeader>

			<CardContent className="px-5 space-y-4">
				{reportingTo && (
					<>
						<div>
							<h3 className="text-lg font-medium text-card-foreground">
								Reporting to
							</h3>
							<div className="flex justify-between items-center mt-1">
								<div>
									<h3 className="text-sm font-semibold text-card-foreground">
										{reportingTo?.name || 'Manager Name'}
									</h3>
									<h4 className="text-xs font-medium text-card-foreground opacity-80">
										{reportingTo?.designation || 'Manager Position'}
									</h4>
								</div>
								<Avatar className="w-10 h-10 rounded-full bg-cover bg-center border-2 border-border">
									<AvatarImage
										src={reportingTo?.profilePhoto || '/placeholder.svg'}
										alt={reportingTo?.name || 'Manager'}
									/>
									<AvatarFallback className="text-sm font-semibold uppercase">
										{reportingTo?.profilePhoto ? (
											getInitials(reportingTo?.name)
										) : (
											<Image
												alt={reportingTo?.name || 'Manager'}
												src={fallbackImage || '/placeholder.svg'}
												className="w-10 h-10 rounded-full object-contain"
												width={40}
												height={40}
											/>
										)}
									</AvatarFallback>
								</Avatar>
							</div>
						</div>

						<Separator />
					</>
				)}

				<div>
					<h3 className="text-lg font-semibold text-card-foreground opacity-80">
						Personal Details
					</h3>
					{personalDetails?.mobile && (
						<div className="mt-2 flex items-center gap-3 text-card-foreground">
							<PhoneCall
								size={18}
								className="text-card-foreground opacity-80"
							/>
							<span className="text-sm">
								{personalDetails?.countryDialCode || ''}
								{personalDetails?.mobile}
							</span>
						</div>
					)}

					{email && (
						<div className="mt-2 flex items-center gap-3 text-card-foreground">
							<Mail size={18} className="text-card-foreground opacity-80" />
							<a
								href={`mailto:${email}`}
								className="text-sm text-blue-600 hover:underline"
							>
								{email}
							</a>
						</div>
					)}

					{personalDetails?.location && (
						<div className="mt-2 flex items-center gap-3 text-gray-600">
							<MapPin size={18} className="text-gray-500" />
							<span className="text-sm">{personalDetails.location}</span>
						</div>
					)}
				</div>

				<Separator />

				<div className="pb-1">
					<div className="flex justify-between items-center">
						<h3 className="text-lg font-semibold text-card-foreground">
							Job Details
						</h3>
						<Button
							onClick={() => setPayslipDialogOpen(true)}
							className="bg-green-100 text-green-800 hover:bg-green-200"
							variant="outline"
							size="icon"
						>
							<DollarSign size={20} />
						</Button>
					</div>
					{personalDetails?.employeeOrgId && (
						<div className="mt-2 flex items-center gap-3 text-card-foreground">
							<span className="font-medium">
								Employee ID: {personalDetails.employeeOrgId}
							</span>
						</div>
					)}

					{department && (
						<div className="mt-2 flex items-center gap-3 text-card-foreground">
							<span className="font-medium">
								Works in {department} department
							</span>
						</div>
					)}

					{personalDetails?.dateOfJoining && (
						<div className="mt-2 flex items-center gap-3 text-card-foreground">
							<span className="font-medium">
								Joined on {formatDate(personalDetails.dateOfJoining)}
							</span>
						</div>
					)}
					<PayslipDialog
						open={payslipDialogOpen}
						setOpen={setPayslipDialogOpen}
					/>
				</div>
			</CardContent>
		</Card>
	);
};

export default ProfileCard;
