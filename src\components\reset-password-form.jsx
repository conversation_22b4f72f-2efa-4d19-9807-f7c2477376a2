'use client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useForm } from 'react-hook-form';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { Eye, EyeOff, Loader2 } from 'lucide-react';
import { resetPasswordSchema } from '@/lib/schemas/authenticationSchema';
import { zodResolver } from '@hookform/resolvers/zod';
import { resetPassword } from '@/lib/features/auth/authSlice';
import { Suspense, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { useRouter } from 'next/navigation';
import { LoadingSubmitButton } from './loading-component';

export default function ResetPasswordForm() {
	const router = useRouter();
	const dispatch = useAppDispatch();
	const [showPassword, setShowPassword] = useState(false);
	const searchParams = useSearchParams();
	const email = searchParams.get('email');
	const token = searchParams.get('token');

	const form = useForm({
		resolver: zodResolver(resetPasswordSchema),
		defaultValues: {
			password: '',
			confirmPassword: '',
		},
	});
	const { isLoading } = useAppSelector((store) => store.auth);

	const onSubmit = async (data) => {
		const payload = { password: data.password, email, token };
		const result = await dispatch(resetPassword(payload));

		if (resetPassword.fulfilled.match(result)) {
			setTimeout(() => {
				router.push('/login');
			}, 3000);
		}
	};

	return (
		<div className="w-full max-w-sm">
			<Form {...form}>
				<form
					className="flex flex-col gap-6"
					onSubmit={form.handleSubmit(onSubmit)}
				>
					<div className="flex flex-col items-center gap-2 text-center">
						<h1 className="text-2xl font-bold">Reset your Password</h1>
						<p className="text-balance text-sm text-muted-foreground">
							Reset your password below
						</p>
					</div>
					<div className="grid gap-6">
						<FormField
							control={form.control}
							name="password"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Password</FormLabel>
									<FormControl>
										<div className="relative w-full">
											<Input
												{...field}
												placeholder="Password"
												type={showPassword ? 'text' : 'password'}
											/>
											<Button
												type="button"
												variant="icon"
												onClick={() => setShowPassword((prev) => !prev)}
												className="absolute inset-y-0 right-0 px-3 flex items-center text-gray-500"
											>
												{showPassword ? (
													<EyeOff size={20} />
												) : (
													<Eye size={20} />
												)}
											</Button>
										</div>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
						<FormField
							control={form.control}
							name="confirmPassword"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Confirm password</FormLabel>
									<FormControl>
										<Input
											{...field}
											type="password"
											autoComplete="confirmPassword"
											autoFocus
											placeholder="Confirm password"
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						<LoadingSubmitButton
							isLoading={isLoading}
							buttonLoadingText={'Setting Password...'}
							buttonText={'Set Password'}
						/>
					</div>
				</form>
			</Form>
		</div>
	);
}
