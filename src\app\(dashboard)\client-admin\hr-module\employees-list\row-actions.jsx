'use client';
import { But<PERSON> } from '@/components/ui/button';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import {
	MoreHorizontal,
	Edit,
	Trash,
	UserCog,
	FileText,
	Mail,
} from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { ScrollArea } from '@/components/ui/scroll-area';

export function DataTableRowActions({ row, dispatch }) {
	const router = useRouter();
	const employee = row.original;
	const [showDeleteDialog, setShowDeleteDialog] = useState(false);

	const handleEdit = () => {
		router.push(`/client-admin/hr-module/employees-list/${employee._id}`);
	};

	const handleViewDetails = () => {
		router.push(`/client-admin/hr-module/employees-list/${employee._id}`);
	};

	const handleManageExperience = () => {
		dispatch({
			type: 'employee/manageExperience',
			payload: employee._id,
		});
		// Navigate to experience management page
		window.location.href = `/employees/${employee._id}/experience`;
	};

	const handleSendEmail = () => {
		const email = employee.personalDetails?.email;
		if (email) {
			window.location.href = `mailto:${email}`;
		} else {
			toast({
				title: 'Error',
				description: 'Email address not available',
				variant: 'destructive',
			});
		}
	};

	const handleDelete = () => {
		// TODO : Implement delete logic
		toast({
			title: 'Employee deleted',
			description: `${
				employee.personalDetails?.fullName ||
				employee.personalDetails?.nameOnNRIC
			} has been deleted.`,
		});
		setShowDeleteDialog(false);
	};

	return (
		<>
			<DropdownMenu>
				<DropdownMenuTrigger asChild>
					<Button variant="ghost" className="h-8 w-8 p-0">
						<span className="sr-only">Open menu</span>
						<MoreHorizontal className="h-4 w-4" />
					</Button>
				</DropdownMenuTrigger>
				<DropdownMenuContent align="end">
					<DropdownMenuLabel>Actions</DropdownMenuLabel>
					<DropdownMenuItem onClick={handleViewDetails}>
						<FileText className="mr-2 h-4 w-4" />
						View Details
					</DropdownMenuItem>
					{/* <DropdownMenuItem onClick={handleEdit}>
						<Edit className="mr-2 h-4 w-4" />
						Edit
					</DropdownMenuItem> */}

					<DropdownMenuSeparator />
					<DropdownMenuItem
						onClick={() => setShowDeleteDialog(true)}
						className="text-destructive focus:text-destructive"
					>
						<Trash className="mr-2 h-4 w-4" />
						Delete
					</DropdownMenuItem>
				</DropdownMenuContent>
			</DropdownMenu>

			<Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>
							Are you sure you want to delete this employee?
						</DialogTitle>
						<DialogDescription>
							This action cannot be undone. This will permanently delete the
							employee record and all associated data.
						</DialogDescription>
					</DialogHeader>
					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => setShowDeleteDialog(false)}
						>
							Cancel
						</Button>
						<Button variant="destructive" onClick={handleDelete}>
							Delete
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>
		</>
	);
}
