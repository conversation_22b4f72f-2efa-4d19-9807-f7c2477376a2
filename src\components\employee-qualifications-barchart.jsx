import {
	<PERSON>,
	<PERSON><PERSON>hart,
	CartesianGrid,
	Cell,
	LabelList,
	XAxis,
	YAxis,
} from 'recharts';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from './ui/chart';
import {
	Card,
	CardContent,
	CardDescription,
	CardFooter,
	CardHeader,
	CardTitle,
} from './ui/card';
import { TrendingUp } from 'lucide-react';

export function EmployeeQualificationsBarChart() {
	const educationData = [
		{
			name: 'High School / Secondary School',
			value: '87',
			fill: 'hsl(var(--chart-1))',
		},
		{
			name: 'Diploma / Associate Degree',
			value: '245',
			fill: 'hsl(var(--chart-2))',
		},
		{ name: "Bachelor's Degree", value: '132', fill: 'hsl(var(--chart-3))' },
		{ name: "Master's Degree", value: '23', fill: 'hsl(var(--chart-4))' },
		{ name: 'Doctorate', value: '23', fill: 'hsl(var(--chart-5))' },
		{
			name: 'Professional Certification',
			value: '56',
			fill: 'hsl(var(--chart-6))',
		},
		{
			name: 'Vocational / Technical Training',
			value: '43',
			fill: 'hsl(var(--chart-7))',
		},
	];

	const educationConfig = {
		'High School / Secondary School': {
			label: 'High School / Secondary School',
			color: 'hsl(var(--chart-1))',
		},
		'Diploma / Associate Degree': {
			label: 'Diploma / Associate Degree',
			color: 'hsl(var(--chart-2))',
		},
		"Bachelor's Degree": {
			label: "Bachelor's Degree",
			color: 'hsl(var(--chart-3))',
		},
		"Master's Degree": {
			label: "Master's Degree",
			color: 'hsl(var(--chart-4))',
		},
		Doctorate: {
			label: 'Doctorate',
			color: 'hsl(var(--chart-5))',
		},
		'Professional Certification': {
			label: 'Professional Certification',
			color: 'hsl(var(--chart-6))',
		},
		'Vocational / Technical Training': {
			label: 'Vocational / Technical Training',
			color: 'hsl(var(--chart-7))',
		},
		label: {
			color: 'hsl(var(--background))',
		},
	};
	return (
		<Card>
			<CardHeader>
				<CardTitle>Education Qualification</CardTitle>
				<CardDescription>
					Total employees based on qualifications
				</CardDescription>
			</CardHeader>
			<CardContent>
				<ChartContainer config={educationConfig}>
					<BarChart
						accessibilityLayer
						data={educationData}
						layout="vertical"
						margin={{
							right: 80,
							left: 10,
						}}
						barSize={40}
					>
						<CartesianGrid horizontal={false} />
						<YAxis
							dataKey="name"
							type="category"
							tickLine={false}
							tickMargin={10}
							axisLine={false}
							hide
						/>
						<XAxis dataKey="value" type="number" hide />
						<ChartTooltip
							cursor={false}
							content={<ChartTooltipContent indicator="line" />}
						/>
						<Bar dataKey="value" layout="vertical" radius={4}>
							{educationData.map((entry, index) => (
								<Cell key={`cell-${index}`} fill={entry.fill} />
							))}
							<LabelList
								dataKey="value"
								position="right"
								offset={8}
								className="fill-foreground font-medium"
								fontSize={12}
							/>
							<LabelList
								dataKey="name"
								position="insideLeft"
								offset={8}
								className="fill-[--color-label] font-medium"
								fontSize={12}
							/>
						</Bar>
					</BarChart>
				</ChartContainer>
			</CardContent>
			<CardFooter className="flex-col items-start gap-2 text-sm">
				<div className="flex gap-2 font-medium leading-none">
					Trending up by 5.2% this month <TrendingUp className="h-4 w-4" />
				</div>
				<div className="leading-none text-muted-foreground">
					Showing total visitors for the last 6 months
				</div>
			</CardFooter>
		</Card>
	);
}
