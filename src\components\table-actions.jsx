'use client';

import { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
	DropdownMenu,
	DropdownMenuCheckboxItem,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ChevronDown, PlusCircle } from 'lucide-react';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from '@/components/ui/dialog';

export function TableActions({
	table,
	handleFilterChange,
	handleBulkAction,
	bulkActions,
}) {
	const [selectedAction, setSelectedAction] = useState(null);
	const [isDialogOpen, setIsDialogOpen] = useState(false);
	const [isDropdownOpen, setIsDropdownOpen] = useState(false);

	useEffect(() => {
		const cleanup = () => {
			// console.log('runn cleanup');
			document.body.style.removeProperty('pointer-events');
		};

		return cleanup;
	}, []);

	useEffect(() => {
		if (isDialogOpen) {
			document.body.style.removeProperty('pointer-events');
		} else if (isDropdownOpen) {
			document.body.style.setProperty('pointer-events', 'auto');
		} else {
			document.body.style.removeProperty('pointer-events');
		}
	}, [isDialogOpen, isDropdownOpen]);

	const handleActionSelect = (action) => {
		setSelectedAction(action);
		setIsDialogOpen(true);
	};

	const handleConfirm = async () => {
		if (selectedAction) {
			await handleBulkAction(selectedAction.value);
			setIsDialogOpen(false);
			setSelectedAction(null);
		}
	};

	return (
		<div className="flex flex-col md:flex-row gap-2 items-center justify-between py-4">
			<Input
				placeholder="Filter data"
				onChange={handleFilterChange}
				className="w-full md:max-w-sm"
			/>
			<div className="flex w-full md:max-w-xs items-center justify-between md:justify-end gap-2">
				<Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
					{bulkActions.length > 0 && (
						<DropdownMenu onOpenChange={setIsDropdownOpen}>
							<DropdownMenuTrigger asChild>
								<Button variant="outline">
									Bulk Actions <ChevronDown className="ml-2 h-4 w-4" />
								</Button>
							</DropdownMenuTrigger>
							<DropdownMenuContent>
								{bulkActions.map((action) => (
									<DropdownMenuItem
										key={action.value}
										onSelect={() => handleActionSelect(action)}
									>
										{action.label}
									</DropdownMenuItem>
								))}
							</DropdownMenuContent>
						</DropdownMenu>
					)}

					<DialogContent>
						<DialogHeader>
							<DialogTitle>Confirm {selectedAction?.label}</DialogTitle>
							<DialogDescription>
								Are you sure you want to {selectedAction?.label.toLowerCase()}?
								This action cannot be undone.
							</DialogDescription>
						</DialogHeader>
						<DialogFooter>
							<Button variant="outline" onClick={() => setIsDialogOpen(false)}>
								Cancel
							</Button>
							<Button variant="destructive" onClick={handleConfirm}>
								Delete
							</Button>
						</DialogFooter>
					</DialogContent>
				</Dialog>
				<DropdownMenu onOpenChange={setIsDropdownOpen}>
					<DropdownMenuTrigger asChild>
						<Button variant="outline">
							Columns <ChevronDown className="ml-2 h-4 w-4" />
						</Button>
					</DropdownMenuTrigger>
					<DropdownMenuContent align="end">
						{table
							.getAllColumns()
							.filter((column) => column.getCanHide())
							.map((column) => (
								<DropdownMenuCheckboxItem
									key={column.id}
									className="capitalize"
									checked={column.getIsVisible()}
									onCheckedChange={(value) => column.toggleVisibility(!!value)}
								>
									{typeof column.columnDef.header === 'string'
										? column.columnDef.header // ✅ If the header is a string, display it
										: column.id}
								</DropdownMenuCheckboxItem>
							))}
					</DropdownMenuContent>
				</DropdownMenu>
			</div>
		</div>
	);
}
