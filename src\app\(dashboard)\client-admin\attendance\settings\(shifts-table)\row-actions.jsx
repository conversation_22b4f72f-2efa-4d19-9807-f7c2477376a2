'use client';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { MoreHorizontal, Edit, Trash, Eye, UserPlus } from 'lucide-react';
import { AssignShiftDialog } from '@/components/assign-shift-dialog';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';

export function DataTableRowActions({ row, dispatch }) {
	const router = useRouter();
	const shift = row.original;
	const [showDeleteDialog, setShowDeleteDialog] = useState(false);
	const [showAssignDialog, setShowAssignDialog] = useState(false);

	const isDefaultShift = shift.isDefault;

	const handleView = () => {
		router.push(`/client-admin/attendance/setting/${shift._id}`);
	};

	const handleDelete = () => {
		dispatch({
			type: 'shift/deleteShift',
			payload: shift._id,
		});

		toast({
			title: 'Shift deleted',
			description: `${shift.name} has been deleted.`,
		});
		setShowDeleteDialog(false);
	};

	return (
		<>
			<DropdownMenu>
				<DropdownMenuTrigger asChild>
					<Button variant="ghost" className="h-8 w-8 p-0">
						<span className="sr-only">Open menu</span>
						<MoreHorizontal className="h-4 w-4" />
					</Button>
				</DropdownMenuTrigger>
				<DropdownMenuContent align="end">
					<DropdownMenuLabel>Actions</DropdownMenuLabel>
					<DropdownMenuItem onClick={handleView}>
						<Eye className="mr-2 h-4 w-4" />
						View Details
					</DropdownMenuItem>
					<DropdownMenuSeparator />
					<DropdownMenuItem onClick={() => setShowAssignDialog(true)}>
						<UserPlus className="mr-2 h-4 w-4 text-blue-500" />
						Assign to Employees
					</DropdownMenuItem>
					<DropdownMenuSeparator />
					{!isDefaultShift && (
						<DropdownMenuItem
							onClick={() => setShowDeleteDialog(true)}
							className="text-destructive focus:text-destructive"
						>
							<Trash className="mr-2 h-4 w-4" />
							Delete
						</DropdownMenuItem>
					)}
				</DropdownMenuContent>
			</DropdownMenu>

			{/* Delete Confirmation Dialog */}
			<AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
				<AlertDialogContent>
					<AlertDialogHeader>
						<AlertDialogTitle>Delete Shift?</AlertDialogTitle>
						<AlertDialogDescription>
							This will permanently delete the shift &quot;{shift.name}&quot;.
							This action cannot be undone.
						</AlertDialogDescription>
					</AlertDialogHeader>
					<AlertDialogFooter>
						<AlertDialogCancel>Cancel</AlertDialogCancel>
						<AlertDialogAction variant="destructive" onClick={handleDelete}>
							Delete
						</AlertDialogAction>
					</AlertDialogFooter>
				</AlertDialogContent>
			</AlertDialog>

			{/* Assign Shift Dialog */}
			<AssignShiftDialog
				open={showAssignDialog}
				onOpenChange={setShowAssignDialog}
				shift={shift}
			/>
		</>
	);
}
