'use client';

import { useState } from 'react';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Check, X, User, Calendar, FileText, AlertCircle } from 'lucide-react';
import { format } from 'date-fns';
import DataComparison from './data-comparison';

const CompareDataDialog = ({
	request,
	open,
	onOpenChange,
	onApprove,
	onReject,
}) => {
	const [showRejectForm, setShowRejectForm] = useState(false);
	const [rejectionReason, setRejectionReason] = useState('');

	const handleApprove = () => {
		onApprove(request._id);
		onOpenChange(false);
	};

	const handleReject = () => {
		if (rejectionReason.trim()) {
			onReject(request._id, rejectionReason);
			onOpenChange(false);
			setShowRejectForm(false);
			setRejectionReason('');
		}
	};

	const formatSectionName = (section) => {
		return section
			.split('-')
			.map((word) => word.charAt(0).toUpperCase() + word.slice(1))
			.join(' ');
	};

	const getStatusBadgeVariant = (status) => {
		switch (status) {
			case 'pending':
				return 'secondary';
			case 'approved':
				return 'default';
			case 'rejected':
				return 'destructive';
			default:
				return 'secondary';
		}
	};

	const isPending = request.status === 'pending';

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
				<DialogHeader>
					<DialogTitle className="flex items-center gap-2">
						<FileText className="h-5 w-5" />
						Compare Data Changes
					</DialogTitle>
					<DialogDescription>
						Review the changes requested by the employee and take appropriate
						action.
					</DialogDescription>
				</DialogHeader>

				<div className="space-y-6">
					{/* Request Information */}
					<div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-muted/50 rounded-lg">
						<div className="space-y-2">
							<div className="flex items-center gap-2">
								<User className="h-4 w-4 text-muted-foreground" />
								<span className="text-sm font-medium">Employee</span>
							</div>
							<div>
								<div className="font-medium">{request.userId.nameOnNRIC}</div>
								<div className="text-sm text-muted-foreground">
									{request.userId.employeeOrgId} • {request.userId.email}
								</div>
							</div>
						</div>

						<div className="space-y-2">
							<div className="flex items-center gap-2">
								<FileText className="h-4 w-4 text-muted-foreground" />
								<span className="text-sm font-medium">Section</span>
							</div>
							<div className="font-medium">
								{formatSectionName(request.section)}
							</div>
						</div>

						<div className="space-y-2">
							<div className="flex items-center gap-2">
								<Calendar className="h-4 w-4 text-muted-foreground" />
								<span className="text-sm font-medium">Submitted</span>
							</div>
							<div className="text-sm">
								{format(new Date(request.submittedAt), 'MMM dd, yyyy HH:mm')}
							</div>
						</div>

						<div className="space-y-2">
							<div className="flex items-center gap-2">
								<AlertCircle className="h-4 w-4 text-muted-foreground" />
								<span className="text-sm font-medium">Status</span>
							</div>
							<Badge variant={getStatusBadgeVariant(request.status)}>
								{request.status.charAt(0).toUpperCase() +
									request.status.slice(1)}
							</Badge>
						</div>
					</div>

					<Separator />

					{/* Data Comparison */}
					<DataComparison
						oldData={request.oldData}
						newData={request.newData}
						section={request.section}
					/>

					{/* Rejection Reason (if rejected) */}
					{request.status === 'rejected' && request.rejectionReason && (
						<>
							<Separator />
							<div className="p-4 bg-red-50 border border-red-200 rounded-lg">
								<div className="flex items-center gap-2 mb-2">
									<X className="h-4 w-4 text-red-600" />
									<span className="font-medium text-red-800">
										Rejection Reason
									</span>
								</div>
								<p className="text-sm text-red-700">
									{request.rejectionReason}
								</p>
								{request.rejectedAt && request.rejectedBy && (
									<div className="mt-2 text-xs text-red-600">
										Rejected by {request.rejectedBy.nameOnNRIC} on{' '}
										{format(new Date(request.rejectedAt), 'MMM dd, yyyy HH:mm')}
									</div>
								)}
							</div>
						</>
					)}

					{/* Approval Info (if approved) */}
					{request.status === 'approved' &&
						request.approvedAt &&
						request.approvedBy && (
							<>
								<Separator />
								<div className="p-4 bg-green-50 border border-green-200 rounded-lg">
									<div className="flex items-center gap-2 mb-2">
										<Check className="h-4 w-4 text-green-600" />
										<span className="font-medium text-green-800">Approved</span>
									</div>
									<div className="text-sm text-green-700">
										Approved by {request.approvedBy.nameOnNRIC} on{' '}
										{format(new Date(request.approvedAt), 'MMM dd, yyyy HH:mm')}
									</div>
								</div>
							</>
						)}

					{/* Reject Form */}
					{showRejectForm && (
						<>
							<Separator />
							<div className="space-y-4">
								<Label htmlFor="rejection-reason">Rejection Reason</Label>
								<Textarea
									id="rejection-reason"
									placeholder="Please provide a reason for rejecting this request..."
									value={rejectionReason}
									onChange={(e) => setRejectionReason(e.target.value)}
									className="min-h-[100px]"
								/>
							</div>
						</>
					)}
				</div>

				<DialogFooter className="flex-col sm:flex-row gap-2">
					{isPending && !showRejectForm && (
						<>
							<Button
								variant="outline"
								onClick={() => setShowRejectForm(true)}
								className="text-red-600 hover:text-red-700 hover:bg-red-50"
							>
								<X className="h-4 w-4 mr-2" />
								Reject
							</Button>
							<Button
								onClick={handleApprove}
								className="bg-green-600 hover:bg-green-700"
							>
								<Check className="h-4 w-4 mr-2" />
								Approve
							</Button>
						</>
					)}

					{showRejectForm && (
						<>
							<Button
								variant="outline"
								onClick={() => {
									setShowRejectForm(false);
									setRejectionReason('');
								}}
							>
								Cancel
							</Button>
							<Button
								variant="destructive"
								onClick={handleReject}
								disabled={!rejectionReason.trim()}
							>
								<X className="h-4 w-4 mr-2" />
								Reject Request
							</Button>
						</>
					)}

					{!isPending && (
						<Button variant="outline" onClick={() => onOpenChange(false)}>
							Close
						</Button>
					)}
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
};

export default CompareDataDialog;
