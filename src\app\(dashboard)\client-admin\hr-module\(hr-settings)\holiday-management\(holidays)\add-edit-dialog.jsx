import React, { useEffect, useState, useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import {
	Command,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
} from '@/components/ui/command';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from '@/components/ui/popover';
import { Switch } from '@/components/ui/switch';
import { Check, ChevronsUpDown } from 'lucide-react';

import { cn } from '@/lib/utils';
import { icons } from '@/data/icons';
import { addHoliday, updateHoliday } from '@/lib/features/holiday/holidaySlice';
import { fetchHolidayGroups } from '@/lib/features/holiday/holidayGroupSlice';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { addUpdateHolidaySchema } from '@/lib/schemas/companySchema';

const getDefaultValues = (isAdd, holiday) => {
	const today = new Date();
	const tomorrow = new Date(today);
	tomorrow.setDate(today.getDate() + 1);

	return isAdd
		? {
				holidayGroupId: '',
				title: '',
				startDate: today.toISOString().split('T')[0],
				endDate: tomorrow.toISOString().split('T')[0],
				icon: '',
			}
		: {
				_id: holiday._id,
				holidayGroupId: holiday.holidayGroupId,
				title: holiday.title,
				startDate: holiday.startDate.split('T')[0],
				endDate: holiday.endDate
					? holiday.endDate.split('T')[0]
					: new Date(
							new Date(holiday.startDate).setDate(
								new Date(holiday.startDate).getDate() + 1
							)
						)
							.toISOString()
							.split('T')[0],
				icon: holiday.icon,
			};
};

const HolidayAddEditDialog = ({
	isAdd,
	title,
	desc,
	holiday,
	showAddEditDialog,
	setShowAddEditDialog,
}) => {
	const [isMultiDay, setIsMultiDay] = useState(
		!isAdd && holiday?.endDate ? true : false
	);
	const dispatch = useAppDispatch();
	const { holidayGroups } = useAppSelector((store) => store.holidayGroup);

	const form = useForm({
		resolver: zodResolver(addUpdateHolidaySchema),
		defaultValues: getDefaultValues(isAdd, holiday),
		mode: 'onSubmit',
		reValidateMode: 'onSubmit',
	});

	// Watch startDate for changes
	const startDate = form.watch('startDate');

	// Update endDate when startDate changes
	useEffect(() => {
		if (startDate) {
			const nextDay = new Date(startDate);
			nextDay.setDate(nextDay.getDate() + 1);
			if (holiday?.endDate) {
				form.setValue(
					'endDate',
					new Date(holiday.endDate).toISOString().split('T')[0]
				);
			} else {
				form.setValue('endDate', nextDay.toISOString().split('T')[0]);
			}
		}
	}, [holiday, startDate, form]);

	// Handle multi-day switch change
	const handleMultiDayChange = (checked) => {
		setIsMultiDay(checked);
		const nextDay = new Date(startDate);
		nextDay.setDate(nextDay.getDate() + 1);
		form.setValue('endDate', nextDay.toISOString().split('T')[0]);
	};

	// Initialize multi-day state when editing
	useEffect(() => {
		if (!isAdd && holiday?.endDate) {
			setIsMultiDay(true);
		}
	}, [isAdd, holiday]);

	useEffect(() => {
		if (!holidayGroups.length) dispatch(fetchHolidayGroups());
	}, [dispatch, holidayGroups.length]);

	const onSubmit = async (data) => {
		const payload = {
			...data,
			startDate: new Date(data.startDate).toISOString(),
			endDate: isMultiDay ? new Date(data.endDate).toISOString() : null,
			...(!isAdd && { _id: data._id }),
		};

		const result = isAdd
			? await dispatch(addHoliday(payload))
			: await dispatch(updateHoliday(payload));

		if (
			addHoliday.fulfilled.match(result) ||
			updateHoliday.fulfilled.match(result)
		) {
			setShowAddEditDialog(false);
		}
	};

	const selectedIcon = useMemo(() => {
		const iconValue = form.watch('icon');
		return icons.find((i) => i.value === iconValue);
	}, [form, form.watch('icon')]);

	return (
		<Dialog open={showAddEditDialog} onOpenChange={setShowAddEditDialog}>
			<DialogContent className="max-h-[80vh] overflow-hidden">
				<DialogHeader>
					<DialogTitle>{title}</DialogTitle>
					<DialogDescription>{desc}</DialogDescription>
				</DialogHeader>

				<Form {...form}>
					<form
						id="holiday-form"
						onSubmit={form.handleSubmit(onSubmit)}
						className="grid gap-4 overflow-y-auto p-2"
					>
						{/* Group & Title */}
						<div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
							<FormField
								control={form.control}
								name="holidayGroupId"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Holiday Group</FormLabel>
										<Popover>
											<PopoverTrigger asChild>
												<FormControl>
													<Button
														variant="outline"
														role="combobox"
														className={cn(
															'w-full justify-between',
															!field.value && 'text-muted-foreground'
														)}
													>
														{holidayGroups.find((g) => g._id === field.value)
															?.name || 'Select group'}
														<ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
													</Button>
												</FormControl>
											</PopoverTrigger>
											<PopoverContent className="w-full p-0">
												<Command>
													<CommandInput placeholder="Search holiday group..." />
													<CommandList>
														<CommandEmpty>No holiday group found.</CommandEmpty>
														<CommandGroup>
															{holidayGroups.map((group) => (
																<CommandItem
																	key={group._id}
																	value={group._id}
																	onSelect={() => field.onChange(group._id)}
																>
																	<Check
																		className={cn(
																			'mr-2 h-4 w-4',
																			group._id === field.value
																				? 'opacity-100'
																				: 'opacity-0'
																		)}
																	/>
																	{group.name}
																</CommandItem>
															))}
														</CommandGroup>
													</CommandList>
												</Command>
											</PopoverContent>
										</Popover>
										<FormMessage />
									</FormItem>
								)}
							/>
							<FormField
								control={form.control}
								name="title"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Holiday Title</FormLabel>
										<FormControl>
											<Input placeholder="e.g. Christmas Day" {...field} />
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>

						{/* Dates */}
						<div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
							<FormField
								control={form.control}
								name="startDate"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Start Date</FormLabel>
										<FormControl>
											<Input
												type="date"
												min={new Date().toISOString().split('T')[0]}
												{...field}
												onChange={(e) => {
													field.onChange(e);
													if (isMultiDay) {
														const nextDay = new Date(e.target.value);
														nextDay.setDate(nextDay.getDate() + 1);
														form.setValue(
															'endDate',
															nextDay.toISOString().split('T')[0]
														);
													}
												}}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
							<FormField
								control={form.control}
								name="endDate"
								render={({ field }) => (
									<FormItem className="flex flex-col gap-1">
										<div className="flex items-end gap-4">
											<FormLabel>End Date</FormLabel>
											<Switch
												id="multi-day"
												checked={isMultiDay}
												onCheckedChange={handleMultiDayChange}
												disabled={!form.getValues('startDate')}
											/>
										</div>
										<FormControl>
											<Input
												type="date"
												disabled={!isMultiDay || !form.getValues('startDate')}
												min={
													startDate
														? new Date(startDate).toISOString().split('T')[0]
														: undefined
												}
												{...field}
												onChange={(e) => {
													const selectedDate = new Date(e.target.value);
													const startDateObj = new Date(startDate);

													if (selectedDate < startDateObj) {
														// If selected date is before start date, set it to start date + 1
														const nextDay = new Date(startDateObj);
														nextDay.setDate(nextDay.getDate() + 1);
														form.setValue(
															'endDate',
															nextDay.toISOString().split('T')[0]
														);
													} else {
														field.onChange(e);
													}
												}}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>

						{/* Icon Picker */}
						<FormField
							control={form.control}
							name="icon"
							render={({ field }) => (
								<FormItem className="flex flex-col">
									<FormLabel>Holiday Icon</FormLabel>
									<Popover>
										<PopoverTrigger asChild>
											<FormControl>
												<Button
													variant="outline"
													role="combobox"
													className={cn(
														'w-full justify-between',
														!field.value && 'text-muted-foreground'
													)}
												>
													<div className="flex items-center">
														{selectedIcon ? (
															<>
																<selectedIcon.icon className="mr-2 h-4 w-4" />
																<span>{selectedIcon.label}</span>
															</>
														) : (
															'Select icon'
														)}
													</div>
													<ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
												</Button>
											</FormControl>
										</PopoverTrigger>
										<PopoverContent className="w-full p-0">
											<Command>
												<CommandInput placeholder="Search icon..." />
												<CommandList>
													<CommandEmpty>No icon found.</CommandEmpty>
													<CommandGroup>
														{icons.map((icon) => (
															<CommandItem
																key={icon.value}
																value={icon.value}
																onSelect={() => field.onChange(icon.value)}
															>
																<icon.icon className="mr-2 h-4 w-4" />
																{icon.label}
																<Check
																	className={cn(
																		'ml-auto h-4 w-4',
																		icon.value === field.value
																			? 'opacity-100'
																			: 'opacity-0'
																	)}
																/>
															</CommandItem>
														))}
													</CommandGroup>
												</CommandList>
											</Command>
										</PopoverContent>
									</Popover>
									<FormDescription>
										Choose an icon to represent this holiday
									</FormDescription>
									<FormMessage />
								</FormItem>
							)}
						/>
					</form>
				</Form>

				<DialogFooter>
					<Button variant="outline" onClick={() => setShowAddEditDialog(false)}>
						Cancel
					</Button>
					<Button type="submit" form="holiday-form">
						Confirm
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
};

export default HolidayAddEditDialog;
