'use client';
import { But<PERSON> } from '@/components/ui/button';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import { MoreHorizontal, Edit, Trash } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';
import AddEditDialog from './add-edit-dialog';
import { deleteDesignation } from '@/lib/features/company-infrastructure/designationSlice';

export function DataTableRowActions({ row, dispatch }) {
	const designation = row.original;
	const [showDeleteDialog, setShowDeleteDialog] = useState(false);
	const [showStatusDialog, setShowStatusDialog] = useState(false);
	const [showAddEditDialog, setShowAddEditDialog] = useState(false);
	const [selectedDesignation, setSelectedDesignation] = useState(null);

	const handleView = () => {
		dispatch({
			type: 'designation/viewDesignation',
			payload: designation._id,
		});
		// Navigate to view page
		window.location.href = `/business-units/${designation._id}`;
	};

	const handleEditClick = () => {
		setSelectedDesignation(designation);
		setShowAddEditDialog(true);
	};

	const handleDelete = async () => {
		const result = await dispatch(deleteDesignation([designation._id]));

		if (deleteDesignation.fulfilled.match(result)) {
			setShowDeleteDialog(false);
		}
	};

	const handleToggleStatus = () => {
		dispatch({
			type: 'designation/toggleStatus',
			payload: {
				id: designation._id,
				deleted: !designation.deleted,
			},
		});

		toast({
			title: designation.deleted
				? 'Designation activated'
				: 'Designation deactivated',
			description: `${designation.name} has been ${
				designation.deleted ? 'activated' : 'deactivated'
			}.`,
		});
		setShowStatusDialog(false);
	};

	return (
		<>
			<DropdownMenu>
				<DropdownMenuTrigger asChild>
					<Button variant="ghost" className="h-8 w-8 p-0">
						<span className="sr-only">Open menu</span>
						<MoreHorizontal className="h-4 w-4" />
					</Button>
				</DropdownMenuTrigger>
				<DropdownMenuContent align="end">
					<DropdownMenuLabel>Actions</DropdownMenuLabel>
					{/* <DropdownMenuItem onClick={handleView}>
						<Eye className="mr-2 h-4 w-4" />
						View Details
					</DropdownMenuItem> */}
					<DropdownMenuItem onClick={handleEditClick}>
						<Edit className="mr-2 h-4 w-4" />
						Edit
					</DropdownMenuItem>
					<DropdownMenuSeparator />
					{/* <DropdownMenuItem onClick={() => setShowStatusDialog(true)}>
						{designation.deleted ? (
							<>
								<CheckCircle className="mr-2 h-4 w-4 text-green-500" />
								Activate
							</>
						) : (
							<>
								<XCircle className="mr-2 h-4 w-4 text-destructive" />
								Deactivate
							</>
						)}
					</DropdownMenuItem> */}
					{/* <DropdownMenuSeparator /> */}
					<DropdownMenuItem
						onClick={() => setShowDeleteDialog(true)}
						className="text-destructive focus:text-destructive"
					>
						<Trash className="mr-2 h-4 w-4" />
						Delete
					</DropdownMenuItem>
				</DropdownMenuContent>
			</DropdownMenu>

			{/* Delete Confirmation Dialog */}
			<Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>
							Are you sure you want to delete this designation?
						</DialogTitle>
						<DialogDescription>
							This action cannot be undone. This will permanently delete the
							designation.
						</DialogDescription>
					</DialogHeader>
					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => setShowDeleteDialog(false)}
						>
							Cancel
						</Button>
						<Button variant="destructive" onClick={handleDelete}>
							Delete
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>

			{/* Status Change Confirmation Dialog */}
			<Dialog open={showStatusDialog} onOpenChange={setShowStatusDialog}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>
							{designation.deleted
								? 'Are you sure you want to activate this designation?'
								: 'Are you sure you want to deactivate this designation?'}
						</DialogTitle>
						<DialogDescription>
							{designation.deleted
								? 'This will make the designation available again.'
								: 'This will make the designation unavailable for new assignments.'}
						</DialogDescription>
					</DialogHeader>
					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => setShowStatusDialog(false)}
						>
							Cancel
						</Button>
						<Button
							variant={designation.deleted ? 'default' : 'destructive'}
							onClick={handleToggleStatus}
						>
							{designation.deleted ? 'Activate' : 'Deactivate'}
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>

			{/* Edit Designation Dialog */}
			{showAddEditDialog && (
				<AddEditDialog
					title="Edit Designation"
					desc="Edit designation details"
					designation={selectedDesignation}
					showAddEditDialog={showAddEditDialog}
					setShowAddEditDialog={setShowAddEditDialog}
				/>
			)}
		</>
	);
}
