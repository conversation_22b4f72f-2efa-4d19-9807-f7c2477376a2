import { customFetch, showErrors } from '@/lib/utils';
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { toast } from 'sonner';
import { getCompanyDetails } from '../company-details/companyDetailsSlice';

const initialState = {
	companies: [],
	employees: [],
	currentCompany: null,
	isLoading: false,
	error: null,
	success: false,
};

export const fetchEmployeesForOrganization = createAsyncThunk(
	'gClientAdmin/fetchEmployeesForOrganization',
	async (companyId, { rejectWithValue }) => {
		try {
			const { data } = await customFetch.get(
				`/glorified-client-admin/employees/${companyId}`
			);
			return data;
		} catch (error) {
			return rejectWithValue(error.response.data);
		}
	}
);

export const addCompany = createAsyncThunk(
	'gClientAdmin/addCompany',
	async (companyData, { rejectWithValue }) => {
		try {
			const { data } = await customFetch.post(
				'/glorified-client-admin/add-company',
				companyData
			);
			return data;
		} catch (error) {
			return rejectWithValue(error.response.data);
		}
	}
);

export const fetchCompanies = createAsyncThunk(
	'gClientAdmin/fetchCompanies',
	async (_, { rejectWithValue }) => {
		try {
			const { data } = await customFetch.get(
				'/glorified-client-admin/companies'
			);
			return data;
		} catch (error) {
			return rejectWithValue(error.response.data);
		}
	}
);

export const changeCompany = createAsyncThunk(
	'gClientAdmin/changeCompany',
	async (selectedCountry, thunkAPI) => {
		try {
			const { data } = await customFetch.get(
				`/glorified-client-admin/change-company/${selectedCountry}`
			);
			if (data.success === true) {
				thunkAPI.dispatch(getCompanyDetails());
			}
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const appointAdmin = createAsyncThunk(
	'gClientAdmin/appointAdmin',
	async ({ employeeId, companyId }, { rejectWithValue }) => {
		try {
			const { data } = await customFetch.patch(
				'/glorified-client-admin/assign-admin',
				{
					employeeId,
					companyId,
				}
			);
			return data;
		} catch (error) {
			return rejectWithValue(error.response.data);
		}
	}
);

export const removeAdmin = createAsyncThunk(
	'gClientAdmin/removeAdmin',
	async ({ clientAdminId, newClientAdminId }, { rejectWithValue }) => {
		try {
			const { data } = await customFetch.patch(
				'/glorified-client-admin/unassign-admin',
				{
					clientAdminId,
					newClientAdminId,
				}
			);
			return data;
		} catch (error) {
			return rejectWithValue(error.response.data);
		}
	}
);

const gClientAdminSlice = createSlice({
	name: 'gClientAdmin',
	initialState,
	reducers: {},
	extraReducers: (builder) => {
		builder
			// Add Company
			.addCase(addCompany.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(addCompany.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				toast.success(payload.message);
			})
			.addCase(addCompany.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			// Fetch Companies
			.addCase(fetchCompanies.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(fetchCompanies.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.companies = payload.data.companies;
			})
			.addCase(fetchCompanies.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			// Change Company
			.addCase(changeCompany.pending, (state, { payload }) => {
				state.isLoading = true;
			})
			.addCase(changeCompany.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				toast.success(payload.message);
			})
			.addCase(changeCompany.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			// Appoint Admin
			.addCase(appointAdmin.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(appointAdmin.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				toast.success(payload.message);
			})
			.addCase(appointAdmin.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			// Remove Admin
			.addCase(removeAdmin.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(removeAdmin.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				toast.success(payload.message);
			})
			.addCase(removeAdmin.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(fetchEmployeesForOrganization.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(
				fetchEmployeesForOrganization.fulfilled,
				(state, { payload }) => {
					state.isLoading = false;
					state.employees = payload.data.employees;
				}
			)
			.addCase(fetchEmployeesForOrganization.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			});
	},
});

export const { resetState } = gClientAdminSlice.actions;
export default gClientAdminSlice.reducer;
