import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { ScrollArea } from '../ui/scroll-area';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';

export function NotificationCard({
	notifications = [
		{
			id: 1,
			fromName: '<PERSON><PERSON><PERSON> (Project Manager)',
			time: '5 mins ago',
			message:
				'Hey, could you go through the updated sprint backlog and ensure your tasks are in sync before the stand-up?',
		},
		{
			id: 2,
			fromName: '<PERSON><PERSON> (DevOps Engineer)',
			time: '30 mins ago',
			message:
				'The latest build was deployed to staging successfully. Please run a quick sanity check when you get a chance.',
		},
		{
			id: 3,
			fromName: '<PERSON><PERSON><PERSON> (Backend Developer)',
			time: '1 hour ago',
			message:
				'Just pushed the latest changes for the auth flow refactor. Ping me if anything looks off or needs tweaking.',
		},
	],
}) {
	return (
		<Card className="w-full h-full">
			<CardHeader>
				<CardTitle>
					Notifications
				</CardTitle>
			</CardHeader>

			<CardContent>
						<ScrollArea className="max-h-[140px]">
				{notifications.map((note) => (
					<Alert
						className="pl-3 border-0 py-1 w-full bg-transparent"
						key={note.id}
					>
						<AlertTitle className="font-semibold">
							{note.fromName}
							<span className="text-card-foreground opacity-80 text-xs ml-2">
								{note.time}
							</span>
						</AlertTitle>
						<AlertDescription className="flex justify-between items-center">
							<span className="pr-2">{note.message}</span>
							<Button size="sm" variant="outline" className="text-xs px-2 py-1">
								Reply
							</Button>
						</AlertDescription>
					</Alert>
				))}
			</ScrollArea>
	</CardContent>
		</Card>
	);
}
