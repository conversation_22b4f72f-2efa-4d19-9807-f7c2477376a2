'use client';
import React, { useState } from 'react';
import { IconSquareRoundedX } from '@tabler/icons-react';
import { MultiStepLoader } from './ui/multi-step-loader';
import { Loader2 } from 'lucide-react';
import { Button } from './ui/button';
import { Progress } from './ui/progress';

const loadingStates = [
	{
		text: 'Processing payroll data',
	},
	{
		text: 'Calculating salaries and deductions',
	},
	{
		text: 'Verifying leave balances',
	},
	{
		text: 'Approving expense claims',
	},
	{
		text: 'Updating employee records',
	},
	{
		text: 'Generating payslips',
	},
	{
		text: 'Syncing attendance logs',
	},
	{
		text: 'Finalizing tax computations',
	},
	{
		text: 'Ensuring compliance with HR policies',
	},
	{
		text: 'Optimizing workforce management',
	},
	{
		text: 'HR magic in progress...',
	},
];

export function Loading({ isLoading }) {
	return (
		<div className="w-full h-[60vh] flex items-center justify-center">
			{/* Core Loader Modal */}
			<MultiStepLoader
				loadingStates={loadingStates}
				loading={isLoading}
				duration={1500}
			/>
		</div>
	);
}

export function SimpleLoader() {
	return <Loader2 className="animate-spin" />;
}

export function LoadingSubmitButton({
	isLoading,
	buttonLoadingText,
	buttonText,
	className,
	variant,
}) {
	return (
		<>
			{isLoading ? (
				<Button
					variant={variant}
					type="submit"
					className={`${className}`}
					disabled={isLoading}
				>
					<Loader2 className="animate-spin mr-2" />
					{buttonLoadingText}
				</Button>
			) : (
				<Button variant={variant} type="submit" className={`${className}`}>
					{buttonText}
				</Button>
			)}
		</>
	);
}

export function LoadingProgressBar({ progress }) {
	return (
		<div className="w-full">
			<Progress value={progress} className="h-2" />
		</div>
	);
}
