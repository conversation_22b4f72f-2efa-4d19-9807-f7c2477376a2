import React from 'react';
import { PlaneIcon, FileTextIcon, HourglassIcon, LockIcon } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';

const LeaveCard = () => {
	return (
		<div className="flex flex-col flex-1 min-w-[280px] max-w-full sm:max-w-auto rounded-xl p-3 space-y-3 text-sm sm:min-h-[100px] min-h-[200px]">
			<div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
				<h2 className="text-base font-semibold text-card-foreground flex items-center gap-2 h-8">
					Apply for Leave
				</h2>
				<div className="h-8 flex items-center gap-2">
					<Button
						variant="outline"
						className="h-8 bg-green-100 text-green-800 hover:bg-green-200"
					>
						Apply
					</Button>
				</div>
			</div>

			<div className="flex-1 overflow-y-auto border-t pt-2 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 text-card-foreground opacity-80">
				Leave Details
			</div>
		</div>
	);
};

export default LeaveCard;
