import React, { useState } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
	Award,
	Book,
	Edit,
	GraduationCap,
	Loader2,
	Plus,
	Save,
	Trash2,
	X,
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { formatDate } from '@/lib/utils';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { updateEmployeeDetailsEducation } from '@/lib/features/employees/updateEmployeeSlice';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from './ui/select';
import { z } from 'zod';

// Define the schema for education details
const educationSchema = z
	.object({
		instituteName: z
			.string()
			.max(100, 'Institute Name must be at most 100 characters long')
			.optional(),

		qualification: z
			.enum(['UNDER_GRADUATE', 'POST_GRADUATE', 'NO_FORMAL_EDUCATION'], {
				errorMap: () => ({
					message: 'Select qualification',
				}),
			})
			.optional(),

		grade: z
			.string()
			.max(10, 'Grade must be at most 10 characters long')
			.optional(),

		startDate: z.string(),
		// .regex(/^\d{4}-\d{2}-\d{2}$/, 'Start date is required'),

		endDate: z.string(),
		// .regex(/^\d{4}-\d{2}-\d{2}$/, 'End Date is required'),

		_id: z.string().optional(),
		// document: imageAndPdfMediaSchema.optional(),
	})
	.superRefine((data, ctx) => {
		if (data.startDate && data.endDate && data.startDate >= data.endDate) {
			ctx.addIssue({
				code: z.ZodIssueCode.custom,
				message: 'End Date must be after Start Date',
				path: ['endDate'], // Error highlights `endDate` field
			});
		}
		if (data.qualification !== 'NO_FORMAL_EDUCATION' || !data.qualification) {
			if (!data.instituteName) {
				ctx.addIssue({
					code: z.ZodIssueCode.custom,
					message: 'Institute name is required',
					path: ['instituteName'],
				});
			}
			if (!data.grade) {
				ctx.addIssue({
					code: z.ZodIssueCode.custom,
					message: 'Grade is required',
					path: ['grade'],
				});
			}
			if (!data.startDate) {
				ctx.addIssue({
					code: z.ZodIssueCode.custom,
					message: 'Start date is required',
					path: ['startDate'],
				});
			}
			if (!data.endDate) {
				ctx.addIssue({
					code: z.ZodIssueCode.custom,
					message: 'End date is required',
					path: ['endDate'],
				});
			}
		}
	});

const skillSchema = z.object({
	skills: z.object({
		hardSkills: z.array(z.string()).default([]),
		softSkills: z.array(z.string()).default([]),
	}),
});

const educationDetailsSchema = z.object({
	education: z.array(educationSchema),
	deletedEducation: z.array(z.string()).default([]),
	...skillSchema.shape,
});

const EmployeeDetailsEducationForm = ({
	employeeId,
	education = [],
	skills = { hardSkills: [], softSkills: [] },
}) => {
	const { isLoading } = useAppSelector((store) => store.employee);
	const dispatch = useAppDispatch();
	const [isEditing, setIsEditing] = useState(false);
	const [newSkill, setNewSkill] = useState('');
	const [skillType, setSkillType] = useState('hard');
	const [originalFormValues, setOriginalFormValues] = useState(null);

	const form = useForm({
		resolver: zodResolver(educationDetailsSchema),
		defaultValues: {
			education: education.map((edu) => ({
				...edu,
				startDate: edu.startDate ? edu.startDate.split('T')[0] : '',
				endDate: edu.endDate ? edu.endDate.split('T')[0] : '',
			})),
			deletedEducation: [],
			skills: {
				hardSkills: skills.hardSkills || [],
				softSkills: skills.softSkills || [],
			},
		},
	});

	const {
		fields: educationFields,
		append: appendEducation,
		remove: removeEducation,
	} = useFieldArray({
		control: form.control,
		name: 'education',
	});

	// Function to handle education deletion
	const handleEducationDelete = (index) => {
		const educationToDelete = form.getValues(`education.${index}`);

		// If the education record has an _id, add it to deletedEducation array
		if (educationToDelete._id) {
			const currentDeletedEducation = form.getValues('deletedEducation') || [];
			form.setValue('deletedEducation', [
				...currentDeletedEducation,
				educationToDelete._id,
			]);
		}

		// Remove the education from the form array
		removeEducation(index);
	};

	// Watch for qualification changes to handle NO_FORMAL_EDUCATION
	const watchEducationFields = form.watch('education');

	const onSubmit = async (data) => {
		console.log('Form data to submit:', data);

		// Format the data for API
		const formattedData = {
			education: data.education.map((edu) => {
				// If NO_FORMAL_EDUCATION, set other fields to null or empty
				if (edu.qualification === 'NO_FORMAL_EDUCATION') {
					return {
						qualification: edu.qualification,
						instituteName: null,
						grade: null,
						startDate: null,
						endDate: null,
						_id: edu._id,
					};
				}

				// Otherwise, format dates properly
				return {
					...edu,
					startDate: new Date(edu.startDate).toISOString().split('T')[0],
					endDate: new Date(edu.endDate).toISOString().split('T')[0],
				};
			}),
			deletedEducation: data.deletedEducation || [],
			skills: data.skills,
			employeeId,
		};

		// Call the API to update education details
		const result = await dispatch(
			updateEmployeeDetailsEducation({
				employeeId,
				...formattedData,
			})
		);

		// Check if update was successful
		if (updateEmployeeDetailsEducation.fulfilled.match(result)) {
			setIsEditing(false);
		}
	};

	const addNewEducation = () => {
		appendEducation({
			instituteName: '',
			qualification: 'UNDER_GRADUATE', // Default to UNDER_GRADUATE
			grade: '',
			startDate: '',
			endDate: '',
		});
	};

	const addSkill = () => {
		if (!newSkill.trim()) return;

		const currentSkills = form.getValues(`skills.${skillType}Skills`) || [];
		if (!currentSkills.includes(newSkill)) {
			form.setValue(`skills.${skillType}Skills`, [...currentSkills, newSkill]);
		}
		setNewSkill('');
	};

	const removeSkill = (type, index) => {
		const currentSkills = [...form.getValues(`skills.${type}Skills`)];
		currentSkills.splice(index, 1);
		form.setValue(`skills.${type}Skills`, currentSkills);
	};

	return (
		<>
			{/* Edit Controls */}
			<div className="flex justify-end mb-4">
				<div className="flex gap-2">
					{isEditing && (
						<Button
							className="bg-red-600 text-white"
							variant="outline"
							onClick={() => {
								// Reset form to original values
								if (originalFormValues) {
									console.log(
										'Restoring original form values:',
										originalFormValues
									);

									// First, reset the form with the original values
									form.reset(originalFormValues);

									// Ensure the watchEducationFields is updated immediately
									// This is needed because the form.watch might not update immediately
									setTimeout(() => {
										// Force a re-render to ensure conditional rendering works correctly
										form.trigger();
									}, 0);
								}
								setNewSkill('');
								setIsEditing(false);
							}}
							disabled={isLoading}
						>
							{<X className="h-4 w-4 mr-2" size={16} />}Cancel
						</Button>
					)}
					<Button
						variant="default"
						onClick={() => {
							if (isEditing) {
								form.handleSubmit(onSubmit)();
							} else {
								// Save original form values before entering edit mode
								const currentValues = form.getValues();
								console.log('Saving original form values:', currentValues);

								// Create a deep copy to ensure we don't have reference issues
								const deepCopy = JSON.parse(JSON.stringify(currentValues));
								setOriginalFormValues(deepCopy);

								setIsEditing(true);
							}
						}}
						disabled={isLoading}
					>
						{isLoading ? (
							<Loader2 className="animate-spin mr-2" size={16} />
						) : isEditing ? (
							<Save className="h-4 w-4 mr-2" size={16} />
						) : (
							<Edit className="h-4 w-4 mr-2" size={16} />
						)}
						{isEditing ? 'Save' : 'Edit'}
					</Button>
				</div>
			</div>

			<div className="grid grid-cols-1 gap-6">
				{/* Educational Qualifications Card */}
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle>Educational Qualifications</CardTitle>
						{isEditing && (
							<Button
								type="button"
								variant="outline"
								size="sm"
								onClick={addNewEducation}
							>
								<Plus className="h-4 w-4 mr-2" />
								Add Education
							</Button>
						)}
					</CardHeader>
					<CardContent>
						{isEditing ? (
							<Form {...form}>
								{educationFields.length > 0 ? (
									<div className="space-y-6">
										{educationFields.map((field, index) => (
											<div
												key={field.id}
												className="space-y-4 p-4 border rounded-md relative"
											>
												<Button
													type="button"
													variant="ghost"
													size="icon"
													className="absolute top-2 right-2 h-6 w-6 text-destructive"
													onClick={() => handleEducationDelete(index)}
												>
													<Trash2 className="h-4 w-4" />
												</Button>

												<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
													<FormField
														control={form.control}
														name={`education.${index}.qualification`}
														render={({ field }) => (
															<FormItem>
																<FormLabel className="text-sm font-medium text-muted-foreground">
																	Qualification
																</FormLabel>
																<Select
																	onValueChange={(value) => {
																		field.onChange(value);

																		// If NO_FORMAL_EDUCATION is selected, clear other fields
																		if (value === 'NO_FORMAL_EDUCATION') {
																			form.setValue(
																				`education.${index}.instituteName`,
																				''
																			);
																			form.setValue(
																				`education.${index}.grade`,
																				''
																			);
																			form.setValue(
																				`education.${index}.startDate`,
																				''
																			);
																			form.setValue(
																				`education.${index}.endDate`,
																				''
																			);
																		}
																	}}
																	defaultValue={field.value}
																>
																	<FormControl>
																		<SelectTrigger>
																			<SelectValue placeholder="Select qualification" />
																		</SelectTrigger>
																	</FormControl>
																	<SelectContent>
																		<SelectItem value="UNDER_GRADUATE">
																			Under Graduate
																		</SelectItem>
																		<SelectItem value="POST_GRADUATE">
																			Post Graduate
																		</SelectItem>
																		<SelectItem value="NO_FORMAL_EDUCATION">
																			No Formal Education
																		</SelectItem>
																	</SelectContent>
																</Select>
																<FormMessage />
															</FormItem>
														)}
													/>

													{watchEducationFields[index]?.qualification !==
														'NO_FORMAL_EDUCATION' && (
														<>
															<FormField
																control={form.control}
																name={`education.${index}.instituteName`}
																render={({ field }) => (
																	<FormItem>
																		<FormLabel className="text-sm font-medium text-muted-foreground">
																			Institute Name
																		</FormLabel>
																		<FormControl>
																			<Input
																				placeholder="Enter institute name"
																				{...field}
																			/>
																		</FormControl>
																		<FormMessage />
																	</FormItem>
																)}
															/>

															<FormField
																control={form.control}
																name={`education.${index}.grade`}
																render={({ field }) => (
																	<FormItem>
																		<FormLabel className="text-sm font-medium text-muted-foreground">
																			Grade/Class
																		</FormLabel>
																		<FormControl>
																			<Input
																				placeholder="Enter grade"
																				{...field}
																			/>
																		</FormControl>
																		<FormMessage />
																	</FormItem>
																)}
															/>

															<FormField
																control={form.control}
																name={`education.${index}.startDate`}
																render={({ field }) => (
																	<FormItem>
																		<FormLabel className="text-sm font-medium text-muted-foreground">
																			Start Date
																		</FormLabel>
																		<FormControl>
																			<Input type="date" {...field} />
																		</FormControl>
																		<FormMessage />
																	</FormItem>
																)}
															/>

															<FormField
																control={form.control}
																name={`education.${index}.endDate`}
																render={({ field }) => (
																	<FormItem>
																		<FormLabel className="text-sm font-medium text-muted-foreground">
																			End Date
																		</FormLabel>
																		<FormControl>
																			<Input type="date" {...field} />
																		</FormControl>
																		<FormMessage />
																	</FormItem>
																)}
															/>
														</>
													)}
												</div>
											</div>
										))}
									</div>
								) : (
									<div className="flex flex-col items-center justify-center py-6 text-center">
										<GraduationCap className="h-8 w-8 text-muted-foreground mb-2" />
										<p className="text-muted-foreground">
											No education details added yet
										</p>
										<Button
											type="button"
											variant="outline"
											size="sm"
											className="mt-4"
											onClick={addNewEducation}
										>
											<Plus className="h-4 w-4 mr-2" />
											Add Education
										</Button>
									</div>
								)}
							</Form>
						) : (
							<>
								{education.length > 0 ? (
									<div className="space-y-6">
										{education.map((edu, index) => (
											<div key={edu._id || index} className="mb-6 last:mb-0">
												<div className="flex flex-col md:flex-row md:justify-between md:items-center gap-2 mb-2">
													<div>
														<h4 className="font-medium">
															{edu.qualification === 'UNDER_GRADUATE'
																? 'Under Graduate'
																: edu.qualification === 'POST_GRADUATE'
																	? 'Post Graduate'
																	: 'No Formal Education'}
														</h4>
														{edu.qualification !== 'NO_FORMAL_EDUCATION' && (
															<p className="text-muted-foreground">
																{edu.instituteName}
															</p>
														)}
													</div>
													{edu.qualification !== 'NO_FORMAL_EDUCATION' && (
														<Badge variant="outline" className="w-fit">
															{formatDate(edu.startDate)} -{' '}
															{formatDate(edu.endDate)}
														</Badge>
													)}
												</div>
												{edu.qualification !== 'NO_FORMAL_EDUCATION' && (
													<p className="text-sm">
														<span className="text-muted-foreground">
															Grade/Class:
														</span>{' '}
														{edu.grade}
													</p>
												)}
												{index < education.length - 1 && (
													<Separator className="my-4" />
												)}
											</div>
										))}
									</div>
								) : (
									<div className="flex items-center justify-center p-6 text-muted-foreground">
										<p>No educational details available</p>
									</div>
								)}
							</>
						)}
					</CardContent>
				</Card>

				{/* Skills Section */}
				<section className="grid md:grid-cols-2 gap-4">
					{/* Soft Skills Card */}
					<Card>
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
							<CardTitle>Soft Skills</CardTitle>
						</CardHeader>
						<CardContent>
							{isEditing ? (
								<Form {...form}>
									<div className="space-y-4">
										<div className="flex gap-2">
											<Input
												placeholder="Add a soft skill"
												value={skillType === 'soft' ? newSkill : ''}
												onChange={(e) => {
													setSkillType('soft');
													setNewSkill(e.target.value);
												}}
												onKeyDown={(e) => {
													if (e.key === 'Enter') {
														e.preventDefault();
														setSkillType('soft');
														addSkill();
													}
												}}
											/>
											<Button
												type="button"
												variant="outline"
												onClick={() => {
													setSkillType('soft');
													addSkill();
												}}
											>
												<Plus className="h-4 w-4" />
											</Button>
										</div>
										<div className="flex flex-wrap gap-2 mt-2">
											{form.watch('skills.softSkills')?.map((skill, index) => (
												<Badge
													key={index}
													variant="secondary"
													className="px-3 py-1"
												>
													{skill}
													<Button
														type="button"
														variant="ghost"
														size="icon"
														className="h-4 w-4 ml-1 text-muted-foreground hover:text-destructive"
														onClick={() => removeSkill('soft', index)}
													>
														<X className="h-3 w-3" />
													</Button>
												</Badge>
											))}
										</div>
									</div>
								</Form>
							) : (
								<div className="flex flex-wrap gap-2">
									{skills.softSkills && skills.softSkills.length > 0 ? (
										skills.softSkills.map((skill, index) => (
											<Badge key={index} variant="secondary">
												{skill}
											</Badge>
										))
									) : (
										<p className="text-muted-foreground">
											No soft skills added
										</p>
									)}
								</div>
							)}
						</CardContent>
					</Card>

					{/* Hard Skills Card */}
					<Card>
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
							<CardTitle>Hard Skills</CardTitle>
						</CardHeader>
						<CardContent>
							{isEditing ? (
								<Form {...form}>
									<div className="space-y-4">
										<div className="flex gap-2">
											<Input
												placeholder="Add a hard skill"
												value={skillType === 'hard' ? newSkill : ''}
												onChange={(e) => {
													setSkillType('hard');
													setNewSkill(e.target.value);
												}}
												onKeyDown={(e) => {
													if (e.key === 'Enter') {
														e.preventDefault();
														setSkillType('hard');
														addSkill();
													}
												}}
											/>
											<Button
												type="button"
												variant="outline"
												onClick={() => {
													setSkillType('hard');
													addSkill();
												}}
											>
												<Plus className="h-4 w-4" />
											</Button>
										</div>
										<div className="flex flex-wrap gap-2 mt-2">
											{form.watch('skills.hardSkills')?.map((skill, index) => (
												<Badge
													key={index}
													variant="secondary"
													className="px-3 py-1"
												>
													{skill}
													<Button
														type="button"
														variant="ghost"
														size="icon"
														className="h-4 w-4 ml-1 text-muted-foreground hover:text-destructive"
														onClick={() => removeSkill('hard', index)}
													>
														<X className="h-3 w-3" />
													</Button>
												</Badge>
											))}
										</div>
									</div>
								</Form>
							) : (
								<div className="flex flex-wrap gap-2">
									{skills.hardSkills && skills.hardSkills.length > 0 ? (
										skills.hardSkills.map((skill, index) => (
											<Badge key={index} variant="secondary">
												{skill}
											</Badge>
										))
									) : (
										<p className="text-muted-foreground">
											No hard skills added
										</p>
									)}
								</div>
							)}
						</CardContent>
					</Card>
				</section>
			</div>
		</>
	);
};

export default EmployeeDetailsEducationForm;
