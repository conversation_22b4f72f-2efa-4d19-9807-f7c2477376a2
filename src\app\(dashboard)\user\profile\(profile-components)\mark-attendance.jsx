import React, { useState, useEffect } from 'react';
import { CoffeeIcon, RefreshCwIcon, Laptop, Clock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Card } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
	Tooltip,
	TooltipContent,
	TooltipTrigger,
} from '@/components/ui/tooltip';
import { addMinutes } from 'date-fns';
import dayjs from 'dayjs';
import ConfirmDialog from '@/components/confirm-dialog';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import {
	getAttendanceLogs,
	getStatus,
	clockIn,
	clockOut,
	endBreak,
	startBreak,
} from '@/lib/features/attendance/attendanceSlice';
import { SimpleLoader } from '@/components/loading-component';

const MarkAttendance = () => {
	const dispatch = useAppDispatch();
	const {
		attendanceLogs,
		currentLogStatus,
		isLoading,
		clockInDelay,
		clockOutDelay,
		clockInLimit,
		clockOutLimit,
	} = useAppSelector((state) => state.attendance);

	const [clockedIn, setClockedIn] = useState(
		currentLogStatus?.status === 'clockIn'
	);
	const [canClockIn, setCanClockIn] = useState(false);
	const [canClockOut, setCanClockOut] = useState(false);
	const [isOnBreak, setIsOnBreak] = useState(false);
	const [clockInTooltipMsg, setClockInTooltipMsg] = useState('');
	const [clockOutTooltipMsg, setClockOutTooltipMsg] = useState('');

	// Real-time clock states
	const [currentTime, setCurrentTime] = useState(new Date());
	const [totalWorkedTime, setTotalWorkedTime] = useState('00:00:00');
	const [clockInTime, setClockInTime] = useState(null);

	// Utility function to format time as HH:MM:SS using dayjs
	const formatTime = (date) => {
		return dayjs(date).format('HH:mm:ss');
	};

	// Utility function to format time as HH:MM for main display
	const formatMainTime = (totalSeconds) => {
		const hours = Math.floor(totalSeconds / 3600);
		const minutes = Math.floor((totalSeconds % 3600) / 60);
		return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
	};

	const checkClockingConditions = () => {
		if (!attendanceLogs || attendanceLogs.length === 0) {
			setCanClockIn(true);
			setClockInTooltipMsg('');
			return;
		}

		const now = new Date();
		const lastLog = attendanceLogs[attendanceLogs.length - 1];
		const lastLogTime = new Date(lastLog?.time);

		const totalClockIns = attendanceLogs.filter(
			(log) => log.type === 'clockIn'
		).length;
		const totalClockOuts = attendanceLogs.filter(
			(log) => log.type === 'clockOut'
		).length;

		if (lastLog?.type === 'start-break') {
			setClockedIn(true);
			setIsOnBreak(true); // you need to add this state to track break status
			setCanClockIn(false);
			setCanClockOut(false);
			setClockOutTooltipMsg('You are on a break');
		} else if (lastLog?.type === 'end-break') {
			setClockedIn(true);
			setCanClockIn(false);
			setCanClockOut(true);
			setIsOnBreak(false);
			setClockOutTooltipMsg('');
			// then apply your normal clock-in conditions here again or just reuse existing logic
		}

		if (lastLog?.type === 'clockIn') {
			setClockedIn(true);
			setCanClockIn(false);
			setClockInTooltipMsg('Already clocked in');

			if (totalClockOuts >= clockOutLimit) {
				setCanClockOut(false);
				setClockOutTooltipMsg(
					`You have reached the daily Clock Out limit (${clockOutLimit})`
				);
			} else if (addMinutes(lastLogTime, clockOutDelay) > now) {
				const waitMins = Math.ceil(
					(addMinutes(lastLogTime, clockOutDelay).getTime() - now.getTime()) /
						60000
				);
				setCanClockOut(false);
				setClockOutTooltipMsg(
					`Please wait ${waitMins} more minute(s) before Clocking Out`
				);
			} else {
				setCanClockOut(true);
				setClockOutTooltipMsg('');
			}
		} else if (lastLog?.type === 'clockOut') {
			setClockedIn(false);
			setCanClockOut(false);
			setClockOutTooltipMsg('Already clocked out');

			if (totalClockIns >= clockInLimit) {
				setCanClockIn(false);
				setClockInTooltipMsg(
					`You have reached the daily Clock In limit (${clockInLimit})`
				);
			} else if (addMinutes(lastLogTime, clockInDelay) > now) {
				const waitMins = Math.ceil(
					(addMinutes(lastLogTime, clockInDelay).getTime() - now.getTime()) /
						60000
				);
				setCanClockIn(false);
				setClockInTooltipMsg(
					`Please wait ${waitMins} more minute(s) before Clocking In`
				);
			} else {
				setCanClockIn(true);
				setClockInTooltipMsg('');
			}
		}
	};

	const getLogDetails = (type) => {
		switch (type) {
			case 'clockIn':
				return {
					title: 'Clock In',
					color: 'bg-green-500',
				};
			case 'clockOut':
				return {
					title: 'Clock Out',
					color: 'bg-red-500',
				};
			case 'start-break':
				return {
					title: 'Start Break',
					color: 'bg-yellow-500',
				};
			case 'end-break':
				return {
					title: 'End Break',
					color: 'bg-blue-500',
				};
			default:
				return {
					title: type,
					color: 'bg-gray-500',
				};
		}
	};

	const onRefresh = () => {
		dispatch(getAttendanceLogs());
		dispatch(getStatus());
	};

	useEffect(() => {
		checkClockingConditions();
	}, [
		attendanceLogs,
		clockInDelay,
		clockOutDelay,
		clockInLimit,
		clockOutLimit,
	]);
	useEffect(() => {
		if (!attendanceLogs) return;
		// Reverse iterate and grab the last break event
		const lastBreakEvent = [...attendanceLogs]
			.reverse()
			.find((log) => log.type === 'start-break' || log.type === 'end-break');
		setIsOnBreak(lastBreakEvent?.type === 'start-break');
	}, [attendanceLogs]);

	useEffect(() => {
		dispatch(getAttendanceLogs());
	}, [dispatch]);

	useEffect(() => {
		dispatch(getStatus());
	}, [dispatch]);

	// Update clock in time when attendance logs change
	useEffect(() => {
		if (!attendanceLogs || attendanceLogs.length === 0) {
			setClockInTime(null);
			return;
		}

		// Find the most recent clock in without a corresponding clock out
		const log = attendanceLogs[attendanceLogs.length - 1];
		let lastClockIn = null;

		if (log.type === 'clockIn' || log.type === 'end-break') {
			lastClockIn = new Date(log.time);
		}

		setClockInTime(lastClockIn);
	}, [attendanceLogs]);

	// Real-time clock update effect
	useEffect(() => {
		const timer = setInterval(() => {
			setCurrentTime(new Date());

			// Calculate total worked time
			if (clockInTime) {
				const now = dayjs();
				const clockIn = dayjs(clockInTime);
				const totalSeconds = now.diff(clockIn, 'second');

				const hours = Math.floor(totalSeconds / 3600);
				const minutes = Math.floor((totalSeconds % 3600) / 60);
				const seconds = totalSeconds % 60;

				setTotalWorkedTime(
					`${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
				);
			} else {
				setTotalWorkedTime('00:00:00');
			}
		}, 1000);

		return () => clearInterval(timer);
	}, [clockInTime]);

	useEffect(() => {
		checkClockingConditions(); // initial check

		// Set up interval only if delay active (clockIn or clockOut)
		const now = new Date();
		const lastLog = attendanceLogs?.[attendanceLogs.length - 1];
		if (!lastLog) return; // no logs, no timer needed

		let delayEndTime;

		if (lastLog.type === 'clockIn') {
			delayEndTime = addMinutes(new Date(lastLog.time), clockOutDelay);
		} else if (lastLog.type === 'clockOut') {
			delayEndTime = addMinutes(new Date(lastLog.time), clockInDelay);
		} else {
			return;
		}

		if (delayEndTime <= now) {
			return; // delay already passed, no timer needed
		}

		const timer = setInterval(() => {
			checkClockingConditions();
		}, 1000 * 30); // every 30 seconds (or 60000 for every minute)

		return () => clearInterval(timer);
	}, [
		attendanceLogs,
		clockInDelay,
		clockOutDelay,
		clockInLimit,
		clockOutLimit,
	]);

	// if (isLoading) return <SimpleLoader />;

	const handleClockIn = async () => {
		await dispatch(clockIn());
		onRefresh();
	};

	const handleClockOut = async () => {
		await dispatch(clockOut());
		onRefresh();
	};

	const handleEndBreak = async () => {
		await dispatch(endBreak());
		onRefresh();
	};
	const handleStartBreak = async () => {
		await dispatch(startBreak());
		onRefresh();
	};

	return (
		<Card className="flex-1 min-w-[320px] max-w-full p-4 space-y-4">
			{/* Header with Title and Controls */}
			<div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
				<h2 className="text-lg font-semibold text-card-foreground flex items-center gap-2">
					Mark Attendance
				</h2>
				<div className="flex items-center gap-2">
					{isLoading && <SimpleLoader />}
					{!clockedIn ? (
						<Tooltip>
							<TooltipTrigger>
								<ConfirmDialog
									renderTrigger={
										<Button
											variant="outline"
											className="bg-green-100 text-green-800 hover:bg-green-200 border-green-300"
											disabled={isLoading || !canClockIn}
										>
											Clock In
										</Button>
									}
									title="Are you sure you want to clock in?"
									description={`You will not be able to Clock Out for the next ${clockInDelay} minutes`}
									confirmTextClassName={
										'bg-green-100 text-green-800 hover:bg-green-200'
									}
									confirmText="Clock In"
									cancelText="Cancel"
									onConfirm={handleClockIn}
								/>
							</TooltipTrigger>
							{!canClockIn && clockInTooltipMsg.length ? (
								<TooltipContent className="bg-red-100 text-red-800">
									<p>{clockInTooltipMsg}</p>
								</TooltipContent>
							) : null}
						</Tooltip>
					) : (
						<>
							{isOnBreak ? (
								<Button
									onClick={handleEndBreak}
									variant="outline"
									size="icon"
									disabled={isLoading}
									className="bg-blue-100 text-blue-800 hover:bg-blue-200 border-blue-300"
								>
									<Laptop />
								</Button>
							) : (
								<Button
									onClick={handleStartBreak}
									variant="outline"
									size="icon"
									disabled={isLoading}
									className="bg-yellow-100 text-yellow-800 hover:bg-yellow-200 border-yellow-300"
								>
									<CoffeeIcon />
								</Button>
							)}
							<Tooltip>
								<TooltipTrigger>
									<ConfirmDialog
										renderTrigger={
											<Button
												variant="outline"
												className="bg-red-100 text-red-800 hover:bg-red-200 border-red-300"
												disabled={isLoading || !canClockOut}
											>
												Clock Out
											</Button>
										}
										title="Are you sure you want to clock out?"
										description={`You will not be able to Clock In for the next ${clockOutDelay} minutes`}
										confirmTextClassName={
											'bg-red-100 text-red-800 hover:bg-red-200'
										}
										confirmText="Clock Out"
										cancelText="Cancel"
										onConfirm={handleClockOut}
									/>
								</TooltipTrigger>
								{!canClockOut && clockOutTooltipMsg.length ? (
									<TooltipContent className="bg-red-100 text-red-800">
										<p>{clockOutTooltipMsg}</p>
									</TooltipContent>
								) : null}
							</Tooltip>
							<Button
								onClick={onRefresh}
								variant="outline"
								size="icon"
								disabled={isLoading}
							>
								<RefreshCwIcon />
							</Button>
						</>
					)}
				</div>
			</div>

			{/* Main Content Area */}
			<div className="flex flex-col lg:flex-row gap-4">
				{/* Left Section - Activity Logs */}
				<div className="flex-1">
					<h3 className="text-sm font-medium text-muted-foreground mb-2">
						Today's Activity
					</h3>
					<ScrollArea className="h-[200px] w-full rounded-md border p-2">
						<div className="space-y-2">
							{attendanceLogs && attendanceLogs.length > 0 ? (
								attendanceLogs.map((log, index) => (
									<div key={index} className="flex items-start gap-2">
										<div
											className={`mt-1.5 h-2 w-2 rounded-full ${getLogDetails(log.type).color}`}
										/>
										<div className="flex-1 space-y-1">
											<div className="flex items-center justify-between">
												<span className="text-sm font-medium">
													{getLogDetails(log.type).title}
												</span>
												<span className="text-xs text-muted-foreground">
													{dayjs(log.time).format('h:mm A')}
												</span>
											</div>
											<p className="text-xs text-muted-foreground">
												{dayjs(log.time).format('MMM DD, YYYY')}
											</p>
										</div>
									</div>
								))
							) : (
								<div className="text-center py-6 text-gray-400">
									<p className="text-sm">No activity yet</p>
								</div>
							)}
						</div>
					</ScrollArea>
				</div>

				{/* Right Section - Total Time Worked Display */}
				<div className="flex flex-col items-center justify-center space-y-2 min-w-[200px]">
					{/* Total Time Worked Title */}
					<h3 className="text-lg font-semibold text-foreground">
						Total Time Worked
					</h3>

					{/* Main Time Display - Large HH:MM */}
					<div className="text-6xl font-bold font-mono text-foreground tracking-wider">
						{clockInTime ? formatMainTime(dayjs().diff(dayjs(clockInTime), 'second')) : '00:00'}
					</div>

					{/* Current Time - Smaller below */}
					<div className="text-lg font-mono text-muted-foreground">
						{formatTime(currentTime)}
					</div>
				</div>
			</div>
		</Card>
	);
};

export default MarkAttendance;
