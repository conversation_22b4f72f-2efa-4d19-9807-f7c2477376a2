'use client';

import { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { Check, ChevronsUpDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
	Command,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
} from '@/components/ui/command';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from '@/components/ui/popover';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import {
	fetchEmployeesForOrganization,
	removeAdmin,
} from '@/lib/features/glorified-client-admin/glorifiedClientAdminSlice';
import { LoadingProgressBar } from './loading-component';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';

export function RemoveAdminDialog({ open, onOpenChange, company }) {
	const dispatch = useAppDispatch();
	const { employees } = useAppSelector((store) => store.glorifiedClientAdmin);
	const [selectedEmployee, setSelectedEmployee] = useState(null);
	const [comboboxOpen, setComboboxOpen] = useState(false);
	const [confirmText, setConfirmText] = useState('');
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [progress, setProgress] = useState(0);

	useEffect(() => {
		if (open && company?._id) {
			dispatch(fetchEmployeesForOrganization(company._id));
		}
	}, [dispatch, company._id, open]);

	const handleRemoveAdmin = async () => {
		if (confirmText !== 'confirm') return;

		setIsSubmitting(true);

		// Start progress animation
		const interval = setInterval(() => {
			setProgress((prev) => {
				if (prev >= 90) {
					clearInterval(interval);
					return 90;
				}
				return prev + 10;
			});
		}, 300);

		try {
			await dispatch(
				removeAdmin({
					clientAdminId: company.clientAdminId,
					newClientAdminId: selectedEmployee?._id || company.owner,
				})
			).unwrap();

			// Complete progress
			setProgress(100);

			// Close dialog after a short delay
			setTimeout(() => {
				onOpenChange(false);
				setSelectedEmployee(null);
				setConfirmText('');
				setProgress(0);
			}, 500);
		} catch (error) {
			console.error('Failed to remove admin:', error);
		} finally {
			clearInterval(interval);
			setIsSubmitting(false);
		}
	};

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="sm:max-w-[425px]">
				<DialogHeader>
					<DialogTitle>Remove Client Admin</DialogTitle>
					<DialogDescription>
						Remove client admin for {company?.businessName}.
						{!selectedEmployee &&
							' If no replacement is selected, the glorified client admin will be assigned.'}
					</DialogDescription>
				</DialogHeader>

				{isSubmitting && (
					<div className="my-4">
						<LoadingProgressBar progress={progress} />
						<p className="text-center text-sm mt-2 font-medium text-amber-600">
							⚠️ Do not reload or refresh — the client admin is being updated.
						</p>
					</div>
				)}

				<div className="grid gap-4 py-4">
					<div className="space-y-2">
						<Label htmlFor="new-admin">New Admin (Optional)</Label>
						<Popover open={comboboxOpen} onOpenChange={setComboboxOpen}>
							<PopoverTrigger asChild>
								<Button
									variant="outline"
									role="combobox"
									aria-expanded={comboboxOpen}
									className="w-full justify-between"
									disabled={isSubmitting}
								>
									{selectedEmployee
										? selectedEmployee.personalDetails.nameOnNRIC
										: 'Select employee (optional)'}
									<ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
								</Button>
							</PopoverTrigger>
							<PopoverContent className="w-[--radix-popover-trigger-width] p-0">
								<Command>
									<CommandInput placeholder="Search employee..." />
									<CommandList>
										<CommandEmpty>No employee found.</CommandEmpty>
										<CommandGroup>
											{employees.map((employee) => (
												<CommandItem
													key={employee._id}
													value={employee.personalDetails.nameOnNRIC}
													onSelect={() => {
														setSelectedEmployee(employee);
														setComboboxOpen(false);
													}}
												>
													<Check
														className={cn(
															'mr-2 h-4 w-4',
															selectedEmployee?._id === employee._id
																? 'opacity-100'
																: 'opacity-0'
														)}
													/>
													{employee.personalDetails.nameOnNRIC}
												</CommandItem>
											))}
										</CommandGroup>
									</CommandList>
								</Command>
							</PopoverContent>
						</Popover>
					</div>

					<div className="space-y-2">
						<Label htmlFor="confirm">Type &quot;confirm&quot; to proceed</Label>
						<Input
							id="confirm"
							value={confirmText}
							onChange={(e) => setConfirmText(e.target.value)}
							disabled={isSubmitting}
							placeholder="confirm"
						/>
					</div>
				</div>

				<DialogFooter>
					<Button
						variant="outline"
						onClick={() => onOpenChange(false)}
						disabled={isSubmitting}
					>
						Cancel
					</Button>
					<Button
						onClick={handleRemoveAdmin}
						disabled={confirmText !== 'confirm' || isSubmitting}
						variant="destructive"
					>
						{isSubmitting ? 'Removing...' : 'Remove Admin'}
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
