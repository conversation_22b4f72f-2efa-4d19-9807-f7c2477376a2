'use client';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import {
	MoreHorizontal,
	Edit,
	Trash,
	Eye,
	UserPlus,
	UserMinus,
	CheckCircle,
	XCircle,
} from 'lucide-react';
import { AppointAdminDialog } from '@/components/appoint-client-admin-dialog';
import { RemoveAdminDialog } from '@/components/remove-client-admin-dialog';
import { toast } from 'sonner';

export function DataTableRowActions({ row, dispatch }) {
	const company = row.original;
	const [showDeleteDialog, setShowDeleteDialog] = useState(false);
	const [showStatusDialog, setShowStatusDialog] = useState(false);
	const [showAppointAdminDialog, setShowAppointAdminDialog] = useState(false);
	const [showRemoveAdminDialog, setShowRemoveAdminDialog] = useState(false);

	const isOwnerAdmin = company.owner === company.clientAdmin;

	const handleView = () => {
		dispatch({
			type: 'company/viewCompany',
			payload: company._id,
		});
		// Navigate to view page
		window.location.href = `/companies/${company._id}`;
	};

	const handleEdit = () => {
		dispatch({
			type: 'company/editCompany',
			payload: company._id,
		});
		// Navigate to edit page
		window.location.href = `/companies/edit/${company._id}`;
	};

	const handleDelete = () => {
		dispatch({
			type: 'company/deleteCompany',
			payload: company._id,
		});

		toast({
			title: 'Company deleted',
			description: `${company.businessName} has been deleted.`,
		});
		setShowDeleteDialog(false);
	};

	const handleToggleStatus = () => {
		dispatch({
			type: 'company/toggleStatus',
			payload: {
				id: company._id,
				deleted: !company.deleted,
			},
		});

		toast({
			title: company.deleted ? 'Company activated' : 'Company deactivated',
			description: `${company.businessName} has been ${company.deleted ? 'activated' : 'deactivated'}.`,
		});
		setShowStatusDialog(false);
	};

	return (
		<>
			<DropdownMenu>
				<DropdownMenuTrigger asChild>
					<Button variant="ghost" className="h-8 w-8 p-0">
						<span className="sr-only">Open menu</span>
						<MoreHorizontal className="h-4 w-4" />
					</Button>
				</DropdownMenuTrigger>
				<DropdownMenuContent align="end">
					<DropdownMenuLabel>Actions</DropdownMenuLabel>
					{/* <DropdownMenuItem onClick={handleView}>
						<Eye className="mr-2 h-4 w-4" />
						View Details
					</DropdownMenuItem>
					<DropdownMenuItem onClick={handleEdit}>
						<Edit className="mr-2 h-4 w-4" />
						Edit
					</DropdownMenuItem> */}

					<DropdownMenuSeparator />

					{isOwnerAdmin ? (
						<DropdownMenuItem onClick={() => setShowAppointAdminDialog(true)}>
							<UserPlus className="mr-2 h-4 w-4 text-blue-500" />
							Appoint Admin
						</DropdownMenuItem>
					) : (
						<DropdownMenuItem onClick={() => setShowRemoveAdminDialog(true)}>
							<UserMinus className="mr-2 h-4 w-4 text-destructive" />
							Remove Admin
						</DropdownMenuItem>
					)}

					<DropdownMenuSeparator />

					{/* <DropdownMenuItem onClick={() => setShowStatusDialog(true)}>
						{company.deleted ? (
							<>
								<CheckCircle className="mr-2 h-4 w-4 text-green-500" />
								Activate
							</>
						) : (
							<>
								<XCircle className="mr-2 h-4 w-4 text-destructive" />
								Deactivate
							</>
						)}
					</DropdownMenuItem> */}

					<DropdownMenuSeparator />

					{/* <DropdownMenuItem
						onClick={() => setShowDeleteDialog(true)}
						className="text-destructive focus:text-destructive"
					>
						<Trash className="mr-2 h-4 w-4" />
						Delete
					</DropdownMenuItem> */}
				</DropdownMenuContent>
			</DropdownMenu>

			{/* Delete Confirmation Dialog */}
			<Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>
							Are you sure you want to delete this company?
						</DialogTitle>
						<DialogDescription>
							This action cannot be undone. This will permanently delete the
							company and all associated data.
						</DialogDescription>
					</DialogHeader>
					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => setShowDeleteDialog(false)}
						>
							Cancel
						</Button>
						<Button variant="destructive" onClick={handleDelete}>
							Delete
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>

			{/* Status Change Confirmation Dialog */}
			<Dialog open={showStatusDialog} onOpenChange={setShowStatusDialog}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>
							{company.deleted
								? 'Are you sure you want to activate this company?'
								: 'Are you sure you want to deactivate this company?'}
						</DialogTitle>
						<DialogDescription>
							{company.deleted
								? 'This will make the company available again.'
								: 'This will make the company unavailable for new assignments.'}
						</DialogDescription>
					</DialogHeader>
					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => setShowStatusDialog(false)}
						>
							Cancel
						</Button>
						<Button
							variant={company.deleted ? 'default' : 'destructive'}
							onClick={handleToggleStatus}
						>
							{company.deleted ? 'Activate' : 'Deactivate'}
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>

			{/* Appoint Admin Dialog */}
			<AppointAdminDialog
				open={showAppointAdminDialog}
				onOpenChange={setShowAppointAdminDialog}
				company={company}
			/>

			{/* Remove Admin Dialog */}
			<RemoveAdminDialog
				open={showRemoveAdminDialog}
				onOpenChange={setShowRemoveAdminDialog}
				company={company}
			/>
		</>
	);
}
