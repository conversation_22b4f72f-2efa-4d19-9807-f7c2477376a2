import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON>, Pencil, Baby, Plus } from 'lucide-react';
import { formatDate } from '../utils/dateUtils';

// Helper function to capitalize strings
const capitalize = (str) => {
	if (!str) return '';
	return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
};

export function FamilyTab({ family, onEditSection, onAddChild }) {
	return (
		<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
			{/* Family Information Card */}
			<Card className="overflow-hidden">
				<CardHeader className="bg-slate-50">
					<div className="flex justify-between items-center">
						<div>
							<CardTitle className="flex items-center gap-2">
								<Users className="h-5 w-5 text-green-600" />
								Family Information
							</CardTitle>
							<CardDescription>
								Your family details. Click edit to request changes.
							</CardDescription>
						</div>
						<Button
							variant="outline"
							size="sm"
							onClick={() => onEditSection('family')}
							className="flex items-center self-start gap-2"
						>
							<Pencil className="h-4 w-4" />
							Edit Section
						</Button>
					</div>
				</CardHeader>
				<CardContent className="pt-6">
					<div className="space-y-4">
						<div>
							<p className="text-sm text-muted-foreground">Marital Status</p>
							<p className="font-medium capitalize">
								{family?.maritalStatus || 'N/A'}
							</p>
						</div>

						{family?.maritalStatus && family.maritalStatus !== 'single' && (
							<>
								<div>
									<p className="text-sm text-muted-foreground">Spouse Name</p>
									<p className="font-medium">{family?.spouseName || 'N/A'}</p>
								</div>
								<div>
									<p className="text-sm text-muted-foreground">
										Spouse Employment Status
									</p>
									<p className="font-medium capitalize">
										{family?.spouseEmploymentStatus || 'N/A'}
									</p>
								</div>
							</>
						)}
					</div>
				</CardContent>
			</Card>

			{/* Children Information Card */}
			<Card className="overflow-hidden">
				<CardHeader className="bg-slate-50">
					<div className="flex justify-between items-center">
						<div>
							<CardTitle className="flex items-center gap-2">
								<Baby className="h-5 w-5 text-blue-600" />
								Children Information
								{family?.children && family.children.length > 0 && (
									<Badge variant="outline">
										{family.children.length} child
										{family.children.length > 1 ? 'ren' : ''}
									</Badge>
								)}
							</CardTitle>
							<CardDescription>
								Information about your children.
							</CardDescription>
						</div>
						<Button
							variant="outline"
							size="sm"
							onClick={onAddChild}
							className="flex items-center self-start gap-2"
						>
							<Plus className="h-4 w-4" />
							Add Child
						</Button>
					</div>
				</CardHeader>
				<CardContent className="pt-6">
					{family?.children && family.children.length > 0 ? (
						<div className="space-y-4">
							{family.children.map((child, index) => (
								<div key={index} className="p-4 border rounded-lg bg-slate-50">
									<div className="flex justify-between items-start mb-2">
										<h4 className="font-medium">{child.name}</h4>
										<Badge variant="outline" className="capitalize">
											{child.gender}
										</Badge>
									</div>
									<div className="grid grid-cols-2 gap-2 text-sm">
										<div>
											<span className="font-medium">DOB:</span>{' '}
											{formatDate(child.dob)}
										</div>
										<div>
											<span className="font-medium">Age:</span>{' '}
											{child.age || 'N/A'}
										</div>
										<div>
											<span className="font-medium">Nationality:</span>{' '}
											{capitalize(child.nationality)}
										</div>
										{child.birthCertificate && (
											<div className="col-span-2">
												<span className="font-medium">Birth Certificate:</span>{' '}
												{child.birthCertificate}
											</div>
										)}
									</div>
								</div>
							))}
						</div>
					) : (
						<div className="text-center py-8 text-muted-foreground">
							<Baby className="h-12 w-12 mx-auto mb-4 opacity-50" />
							<p>No children information available</p>
							<p className="text-sm">
								Click &quot;Add Child&quot; to add information
							</p>
						</div>
					)}
				</CardContent>
			</Card>
		</div>
	);
}
