'use client';

import * as React from 'react';
import {
	ArchiveX,
	Banknote,
	Building,
	Building2,
	CalendarCheck,
	CalendarCheck2,
	CalendarDays,
	ChevronRight,
	ClipboardCheck,
	ClipboardList,
	Command,
	Earth,
	File,
	FileCheck,
	FileClock,
	Fingerprint,
	HandCoins,
	Home,
	Inbox,
	Landmark,
	List,
	Megaphone,
	MessageSquareText,
	Send,
	Settings,
	ShieldCheck,
	Sparkles,
	Trash2,
	TreePalm,
	User,
	Users,
} from 'lucide-react';
import logo from '@/assets/valluva.png';
import { NavUser } from '@/components/nav-user';
import { Label } from '@/components/ui/label';
import {
	Sidebar,
	SidebarContent,
	SidebarFooter,
	SidebarGroup,
	SidebarGroupContent,
	SidebarHeader,
	SidebarInput,
	SidebarMenu,
	SidebarMenuButton,
	SidebarMenuItem,
	SidebarMenuSub,
	SidebarMenuSubButton,
	SidebarMenuSubItem,
	useSidebar,
} from '@/components/ui/sidebar';
import { Switch } from '@/components/ui/switch';
import Image from 'next/image';
import Link from 'next/link';
import {
	Collapsible,
	CollapsibleContent,
	CollapsibleTrigger,
} from './ui/collapsible';
import ChatUserList from './chat-user-list';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { ChatsList } from './chats-list';

// This is sample data
const data = {
	user: {
		name: 'Client Admin',
		email: '<EMAIL>',
		avatar: '/avatars/shadcn.jpg',
	},
	clientNav: [
		// {
		// 	title: 'Dashboard',
		// 	url: '/client-admin/',
		// 	icon: Home,
		// 	isActive: true,
		// },
		{
			title: 'Profile',
			url: '/client-admin/profile/',
			icon: User,
			isActive: false,
			secondaryMenus: [
				{
					icon: User,
					title: 'Profile',
					url: '#',
					items: [
						{
							title: 'Edit Profile',
							url: '/client-admin/profile/edit/',
						},
					],
				},
			],
		},
		{
			title: 'Chat',
			url: '/client-admin/chat/',
			icon: MessageSquareText,
			isActive: false,
			secondaryMenus: [
				{
					icon: Home,
					title: 'Home',
					url: '#',
					items: [
						{
							title: 'Chat Dashboard',
							url: '/client-admin/chat/',
						},
					],
				},
				{
					icon: Inbox,
					title: 'Messages',
					url: '#',
					items: [
						{
							title: 'Inbox',
							url: '/client-admin/chat/inbox/',
						},
						{
							title: 'Sent Messages',
							url: '/client-admin/chat/sent/',
						},
					],
				},
				{
					icon: Settings,
					title: 'Settings',
					url: '#',
					items: [
						{
							title: 'Chat Settings',
							url: '/client-admin/chat/settings/',
						},
						{
							title: 'Message Templates',
							url: '/client-admin/chat/templates/',
						},
					],
				},
			],
		},
		// TODO: add clock in in profile page
		// {
		// 	title: 'Clock In',
		// 	url: '#',
		// 	icon: Fingerprint,
		// 	isActive: false,
		// },
		{
			title: 'HR Module',
			url: '/client-admin/hr-module/',
			icon: Users,
			isActive: true,
			secondaryMenus: [
				{
					icon: Home,
					title: 'HR Dashboard',
					url: '#',
					items: [
						{
							title: 'Overview',
							url: '/client-admin/hr-module/',
						},
					],
				},
				{
					icon: List,
					title: 'List',
					url: '#',
					items: [
						{
							title: 'Employees List',
							url: '/client-admin/hr-module/employees-list/',
						},
						{
							title: 'Verify & Update',
							url: '/client-admin/hr-module/verify-updates/',
						},
					],
				},
				{
					icon: Users,
					title: 'Employee Transitions',
					url: '#',
					items: [
						{
							title: 'Onboarding',
							url: '/client-admin/hr-module/employee-onboarding/',
						},
						{
							title: 'Offboarding',
							url: '/client-admin/hr-module/employee-offboarding/',
						},
					],
				},
				{
					icon: Settings,
					title: 'Settings',
					url: '#',
					items: [
						{
							title: 'Administration',
							url: '/client-admin/hr-module/administration/',
						},
						{
							title: 'Holiday Management',
							url: '/client-admin/hr-module/holiday-management/',
						},
						// {
						// 	title: 'Manage Module Admin',
						// 	url: '/client-admin/hr-module/module-admin/',
						// },
					],
				},
			],
		},
		{
			title: 'Communication',
			url: '#',
			icon: Megaphone,
			isActive: false,
			secondaryMenus: [
				{ icon: Home, title: 'Home', url: '#', items: [] },
				{ icon: FileCheck, title: 'Submissions', url: '#', items: [] },
				{ icon: Settings, title: 'Settings', url: '#', items: [] },
			],
		},
		{
			title: 'Attendance',
			url: '/client-admin/attendance/',
			icon: CalendarDays,
			isActive: false,
			secondaryMenus: [
				{
					icon: Home,
					title: 'Overview',
					url: '#',
					items: [
						{
							title: 'Attendance Dashboard',
							url: '/client-admin/attendance/',
						},
					],
				},
				{
					icon: CalendarCheck,
					title: 'Shift Roster',
					url: '#',
					items: [
						{
							title: 'Shift Management',
							url: '/client-admin/attendance/shift-management/',
						},
					],
				},
				{
					icon: Settings,
					title: 'Settings',
					url: '#',
					items: [
						{
							title: 'Attendance Settings',
							url: '/client-admin/attendance/settings/',
						},
					],
				},
			],
		},
		{
			title: 'Expense Claim',
			url: '#',
			icon: HandCoins,
			isActive: false,
			secondaryMenus: [
				{ icon: Home, title: 'Home', url: '#', items: [] },
				{ icon: List, title: 'List', url: '#', items: [] },
				{ icon: ClipboardList, title: 'Report', url: '#', items: [] },
				{ icon: Settings, title: 'Claim Settings', url: '#', items: [] },
			],
		},
		{
			title: 'Leave Management',
			url: '#',
			icon: TreePalm,
			isActive: false,
			secondaryMenus: [
				{ icon: Home, title: 'Home', url: '#', items: [] },
				{ icon: List, title: 'List', url: '#', items: [] },
				{ icon: ClipboardList, title: 'Report', url: '#', items: [] },
				{ icon: Settings, title: 'Claim Settings', url: '#', items: [] },
			],
		},
		{
			title: 'Payroll',
			url: '#',
			icon: Banknote,
			isActive: false,
			secondaryMenus: [
				{ icon: Home, title: 'Home', url: '#', items: [] },
				{ icon: List, title: 'List', url: '#', items: [] },
				{ icon: ShieldCheck, title: 'Compliance', url: '#', items: [] },
				{ icon: ClipboardList, title: 'Report', url: '#', items: [] },
				{ icon: Landmark, title: 'Bank', url: '#', items: [] },
				{ icon: Settings, title: 'Settings', url: '#', items: [] },
			],
		},
		{
			title: 'Projects and Tasks',
			url: '#',
			icon: ClipboardCheck,
			isActive: false,
			secondaryMenus: [
				{ icon: Home, title: 'Home', url: '#', items: [] },
				{ icon: ClipboardCheck, title: 'Task', url: '#', items: [] },
				{ icon: ClipboardList, title: 'Report', url: '#', items: [] },
				{ icon: Settings, title: 'Settings', url: '#', items: [] },
			],
		},
		{
			title: 'Performance and Appraisals',
			url: '#',
			icon: Sparkles,
			isActive: false,
		},
		{
			title: 'Settings',
			url: '#',
			icon: Settings,
			isActive: false,
			secondaryMenus: [
				{ icon: Building, title: 'Business', url: '#', items: [] },
				{ icon: Building2, title: 'Branch', url: '#', items: [] },
				{ icon: Earth, title: 'Brand', url: '#', items: [] },
				{ icon: FileClock, title: 'Audit Log', url: '#', items: [] },
			],
		},
	],
	// mails: [
	// 	{
	// 		name: 'William Smith',
	// 		email: '<EMAIL>',
	// 		subject: 'Meeting Tomorrow',
	// 		date: '09:34 AM',
	// 		teaser:
	// 			'Hi team, just a reminder about our meeting tomorrow at 10 AM.\nPlease come prepared with your project updates.',
	// 	},
	// 	{
	// 		name: 'Alice Smith',
	// 		email: '<EMAIL>',
	// 		subject: 'Re: Project Update',
	// 		date: 'Yesterday',
	// 		teaser:
	// 			"Thanks for the update. The progress looks great so far.\nLet's schedule a call to discuss the next steps.",
	// 	},
	// 	{
	// 		name: 'Bob Johnson',
	// 		email: '<EMAIL>',
	// 		subject: 'Weekend Plans',
	// 		date: '2 days ago',
	// 		teaser:
	// 			"Hey everyone! I'm thinking of organizing a team outing this weekend.\nWould you be interested in a hiking trip or a beach day?",
	// 	},
	// 	{
	// 		name: 'Emily Davis',
	// 		email: '<EMAIL>',
	// 		subject: 'Re: Question about Budget',
	// 		date: '2 days ago',
	// 		teaser:
	// 			"I've reviewed the budget numbers you sent over.\nCan we set up a quick call to discuss some potential adjustments?",
	// 	},
	// 	{
	// 		name: 'Michael Wilson',
	// 		email: '<EMAIL>',
	// 		subject: 'Important Announcement',
	// 		date: '1 week ago',
	// 		teaser:
	// 			"Please join us for an all-hands meeting this Friday at 3 PM.\nWe have some exciting news to share about the company's future.",
	// 	},
	// 	{
	// 		name: 'Sarah Brown',
	// 		email: '<EMAIL>',
	// 		subject: 'Re: Feedback on Proposal',
	// 		date: '1 week ago',
	// 		teaser:
	// 			"Thank you for sending over the proposal. I've reviewed it and have some thoughts.\nCould we schedule a meeting to discuss my feedback in detail?",
	// 	},
	// 	{
	// 		name: 'David Lee',
	// 		email: '<EMAIL>',
	// 		subject: 'New Project Idea',
	// 		date: '1 week ago',
	// 		teaser:
	// 			"I've been brainstorming and came up with an interesting project concept.\nDo you have time this week to discuss its potential impact and feasibility?",
	// 	},
	// 	{
	// 		name: 'Olivia Wilson',
	// 		email: '<EMAIL>',
	// 		subject: 'Vacation Plans',
	// 		date: '1 week ago',
	// 		teaser:
	// 			"Just a heads up that I'll be taking a two-week vacation next month.\nI'll make sure all my projects are up to date before I leave.",
	// 	},
	// 	{
	// 		name: 'James Martin',
	// 		email: '<EMAIL>',
	// 		subject: 'Re: Conference Registration',
	// 		date: '1 week ago',
	// 		teaser:
	// 			"I've completed the registration for the upcoming tech conference.\nLet me know if you need any additional information from my end.",
	// 	},
	// 	{
	// 		name: 'Sophia White',
	// 		email: '<EMAIL>',
	// 		subject: 'Team Dinner',
	// 		date: '1 week ago',
	// 		teaser:
	// 			"To celebrate our recent project success, I'd like to organize a team dinner.\nAre you available next Friday evening? Please let me know your preferences.",
	// 	},
	// ],
};

export function AppSidebar({ ...props }) {
	// Note: I'm using state to show active item.
	// IRL you should use the url/router.
	// const [mails, setMails] = React.useState(data.mails);
	const [activeItem, setActiveItem] = React.useState(data.clientNav[0]);
	const [showChat, setShowChat] = React.useState(false); // New state for chat visibility
	const { setOpen } = useSidebar();

	return (
		<Sidebar
			variant="inset"
			collapsible="icon"
			className="overflow-hidden [&>[data-sidebar=sidebar]]:flex-row"
			{...props}
		>
			{/* This is the first sidebar */}
			{/* We disable collapsible and adjust width to icon. */}
			{/* This will make the sidebar appear as icons. */}
			<Sidebar
				variant="inset"
				collapsible="none"
				className="!w-[calc(var(--sidebar-width-icon)_+_1px)] mr-4"
			>
				<SidebarHeader>
					<SidebarMenu>
						<SidebarMenuItem>
							<SidebarMenuButton size="lg" asChild className="md:h-8 md:p-0">
								<Link href="/client-admin">
									<div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-transparent text-sidebar-primary-foreground">
										<Image
											src={logo}
											width={64}
											height={64}
											className="object-fit size-6"
											alt="Harp HR"
										/>
									</div>
									<div className="grid flex-1 text-left text-sm leading-tight">
										<span className="truncate font-semibold">Valluva</span>
									</div>
								</Link>
							</SidebarMenuButton>
						</SidebarMenuItem>
					</SidebarMenu>
				</SidebarHeader>
				<SidebarContent>
					<SidebarGroup>
						<SidebarGroupContent className="px-1.5 md:px-0">
							<SidebarMenu>
								{data.clientNav.map((item) => (
									<SidebarMenuItem key={item.title}>
										<Link href={item.url}>
											<SidebarMenuButton
												tooltip={{
													children: item.title,
													hidden: false,
												}}
												onClick={() => {
													setActiveItem(item);
													setShowChat(item.title === 'Chat'); // Enable chat view when "Chat" is clicked
													setOpen(true);
												}}
												isActive={activeItem.title === item.title}
											>
												<item.icon />
												<span>{item.title}</span>
											</SidebarMenuButton>
										</Link>
									</SidebarMenuItem>
								))}
							</SidebarMenu>
						</SidebarGroupContent>
					</SidebarGroup>
				</SidebarContent>
				<SidebarFooter>
					<NavUser user={data.user} />
				</SidebarFooter>
			</Sidebar>
			{/* This is the second sidebar */}
			{/* We disable collapsible and let it fill remaining space */}
			<Sidebar
				variant="inset"
				collapsible="none"
				className="hidden flex-1 md:flex shadow-lg rounded-xl"
			>
				<SidebarHeader className="gap-3.5 p-4 max-h-16">
					<div className="flex w-full items-center justify-between">
						<div className="text-base font-medium text-foreground">
							{activeItem.title}
						</div>
						<Label className="flex items-center gap-2 text-sm">
							<activeItem.icon />
						</Label>
					</div>
				</SidebarHeader>
				<SidebarContent>
					<SidebarGroup className="px-0">
						{showChat ? (
							<ChatsList />
						) : (
							<SidebarMenu>
								{activeItem.secondaryMenus?.map((item) => (
									<Collapsible
										key={item.title}
										asChild
										defaultOpen
										className="group/collapsible"
									>
										<SidebarMenuItem>
											<CollapsibleTrigger asChild>
												<SidebarMenuButton tooltip={item.title}>
													{item.icon && <item.icon />}
													<span>{item.title}</span>
													<ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
												</SidebarMenuButton>
											</CollapsibleTrigger>
											<CollapsibleContent>
												<SidebarMenuSub>
													{item.items?.map((subItem) => (
														<SidebarMenuSubItem key={subItem.title}>
															<SidebarMenuSubButton asChild>
																<Link href={subItem.url}>
																	<span>{subItem.title}</span>
																</Link>
															</SidebarMenuSubButton>
														</SidebarMenuSubItem>
													))}
												</SidebarMenuSub>
											</CollapsibleContent>
										</SidebarMenuItem>
									</Collapsible>
								))}
							</SidebarMenu>
						)}
					</SidebarGroup>
				</SidebarContent>
			</Sidebar>
		</Sidebar>
	);
}
