import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { customFetch, showErrors } from '@/lib/utils';
import { toast } from 'sonner';
import {
	setClientAdminIdAsEmployeeId,
	setEmployeeRegistrationSteps,
} from '../employees/employeeSlice';

const initialState = {
	companyData: null,
	clockIn: false,
	isBreakTime: false,
	breakTime: [],
	clockInTime: null,
	clockOutTime: null,
	isLoading: false,
};

export const getCompanyDetails = createAsyncThunk(
	'companyDetails/getCompanyDetails',
	async (_, thunkAPI) => {
		try {
			const { data } = await customFetch('/company-details', {
				withCredentials: true,
			});
			if (data.data.ownerUserFlags) {
				const {
					isPersonalDetailsComplete,
					isQualificationDetailsComplete,
					isContactDetailsComplete,
					isEmploymentDetailsComplete,
					isEarningsDetailsComplete,
					isBenefitsDetailsComplete,
					isClientRegistrationAsEmployeeComplete,
				} = data.data.ownerUserFlags;
				if (isClientRegistrationAsEmployeeComplete === false) {
					thunkAPI.dispatch(
						setClientAdminIdAsEmployeeId(data.data.companyDetails?.owner)
					);
				}
				// if (isBenefitsDetailsComplete === false) {
				// 	thunkAPI.dispatch(setEmployeeRegistrationSteps(6));
				// }
				if (isEarningsDetailsComplete === false) {
					thunkAPI.dispatch(setEmployeeRegistrationSteps(5));
				}
				if (isEmploymentDetailsComplete === false) {
					thunkAPI.dispatch(setEmployeeRegistrationSteps(4));
				}
				if (isContactDetailsComplete === false) {
					thunkAPI.dispatch(setEmployeeRegistrationSteps(3));
				}
				if (isQualificationDetailsComplete === false) {
					thunkAPI.dispatch(setEmployeeRegistrationSteps(2));
				}
				if (isPersonalDetailsComplete === false) {
					thunkAPI.dispatch(setEmployeeRegistrationSteps(1));
				}
			}
			// console.log(data);
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const updateWorkParameters = createAsyncThunk(
	'companyDetails/updateWorkParameters',
	async (workParametersDetails, thunkAPI) => {
		try {
			const { data } = await customFetch.patch(
				'/company-details/work-parameters',
				workParametersDetails
			);

			if (data?.success) {
				thunkAPI.dispatch(getCompanyDetails());
			}

			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

const companyDetailsSlice = createSlice({
	name: 'companyDetails',
	initialState,
	reducers: {
		setClockIn: (state, { payload }) => {
			state.clockIn = true;
			state.clockInTime = payload;
		},
		clockOut: (state, { payload }) => {
			state.clockIn = false;
			state.clockOutTime = payload;
		},
		setIsBreakTime: (state, { payload }) => {
			state.isBreakTime = !state.isBreakTime;
			state.breakTime.push(payload);
		},
	},
	extraReducers: (builder) => {
		builder
			.addCase(getCompanyDetails.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(getCompanyDetails.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.companyData = payload.data;
			})
			.addCase(getCompanyDetails.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(updateWorkParameters.pending, (state) => {
				state.isLoading = true;
				toast.info('Updating Work Parameters...');
			})
			.addCase(updateWorkParameters.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				toast.success(payload.message);
			})
			.addCase(updateWorkParameters.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			});
	},
});

export const { setClockIn, clockOut, setIsBreakTime, setClockInTime } =
	companyDetailsSlice.actions;
export default companyDetailsSlice.reducer;
