import { ShiftSettingForm } from '@/components/shift-settings-form';
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import React from 'react';
import ShiftsTable from './(shifts-table)/shifts-table';

export default function ShiftSettingsPage() {
	return (
		<section className="w-full h-full p-4">
			<header className="flex flex-col gap-2">
				<h1 className="text-xl md:text-2xl font-semibold">
					Attendance Settings
				</h1>
				<p className="font-medium text-gray-500">
					Manage shifts for employees.
				</p>
				<Separator />
			</header>
			<main className="grid flex-1 items-start gap-4 md:gap-8 w-full mt-4">
				<Tabs defaultValue={'create-shift'}>
					<div className="flex items-center">
						<TabsList>
							<TabsTrigger
								value={'create-shift'}
								className="flex-shrink-0 text-sm md:text-md whitespace-nowrap px-2 md:px-4"
							>
								Create Shift{' '}
							</TabsTrigger>
							<TabsTrigger
								value={'manage-shifts'}
								className="flex-shrink-0 text-sm md:text-md whitespace-nowrap px-2 md:px-4"
							>
								Manage Shifts{' '}
							</TabsTrigger>
						</TabsList>
					</div>
					<TabsContent value={'create-shift'}>
						<Card>
							<CardHeader className="flex flex-row justify-between">
								<div>
									<CardTitle>Create Shift</CardTitle>
									<CardDescription>
										Create a new shift for attendance
									</CardDescription>
								</div>
							</CardHeader>
							<CardContent>
								<ShiftSettingForm />
							</CardContent>
						</Card>
					</TabsContent>
					<TabsContent value={'manage-shifts'}>
						<Card>
							<CardHeader className="flex flex-row justify-between">
								<div>
									<CardTitle>Manage Shifts</CardTitle>
									<CardDescription>
										Manage the shifts for attendance
									</CardDescription>
								</div>
							</CardHeader>
							<CardContent>
								<ShiftsTable />
							</CardContent>
						</Card>
					</TabsContent>
				</Tabs>
			</main>
		</section>
	);
}
