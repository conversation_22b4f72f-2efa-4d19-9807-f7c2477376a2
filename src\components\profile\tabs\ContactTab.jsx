import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Phone, Pencil, Mail, MapPin } from 'lucide-react';

// Helper function to capitalize strings
const capitalize = (str) => {
	if (!str) return '';
	return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
};

export function ContactTab({ contact, onEditSection }) {
	return (
		<div className="grid grid-cols-1 gap-6">
			<Card className="overflow-hidden">
				<CardHeader className="bg-slate-50">
					<div className="flex justify-between items-center">
						<div>
							<CardTitle className="flex items-center gap-2">
								<Phone className="h-5 w-5 text-teal-600" />
								Contact Information
								{contact && contact.length > 0 && (
									<Badge variant="outline">
										{contact.length} contact{contact.length > 1 ? 's' : ''}
									</Badge>
								)}
							</CardTitle>
							<CardDescription>
								Your emergency and other contacts. Click edit to request
								changes.
							</CardDescription>
						</div>
						<Button
							variant="outline"
							size="sm"
							onClick={() => onEditSection('contact')}
							className="flex items-center self-start gap-2"
						>
							<Pencil className="h-4 w-4" />
							Edit Section
						</Button>
					</div>
				</CardHeader>
				<CardContent className="pt-6">
					{contact && contact.length > 0 ? (
						<div className="space-y-4">
							{contact.map((cont, index) => (
								<div key={index} className="p-4 border rounded-lg bg-slate-50">
									<div className="flex justify-between items-start mb-2">
										<div>
											<h4 className="font-medium">{cont.name}</h4>
											<p className="text-sm text-muted-foreground">
												{cont.relationship}
											</p>
										</div>
										<Badge variant="outline" className="capitalize">
											{cont.type}
										</Badge>
									</div>
									<div className="space-y-2 text-sm">
										<div className="flex items-center gap-2">
											<Phone className="h-4 w-4 text-muted-foreground" />
											<span>{cont.phone || 'N/A'}</span>
										</div>
										{cont.email && (
											<div className="flex items-center gap-2">
												<Mail className="h-4 w-4 text-muted-foreground" />
												<span>{cont.email}</span>
											</div>
										)}
										{cont.address && (
											<div className="flex items-start gap-2">
												<MapPin className="h-4 w-4 text-muted-foreground mt-0.5" />
												<span>{cont.address}</span>
											</div>
										)}
									</div>
								</div>
							))}
						</div>
					) : (
						<div className="text-center py-8 text-muted-foreground">
							<Phone className="h-12 w-12 mx-auto mb-4 opacity-50" />
							<p>No contact information available</p>
							<p className="text-sm">
								Click &quot;Edit Section&quot; to add emergency contacts
							</p>
						</div>
					)}
				</CardContent>
			</Card>
		</div>
	);
}
